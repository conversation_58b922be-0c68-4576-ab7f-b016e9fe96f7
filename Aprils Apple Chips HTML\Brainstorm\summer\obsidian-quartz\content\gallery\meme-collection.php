<?php
// Auto-generated blog post
// Source: meme-collection.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Meme Collection';
$meta_description = 'This is a collection of internet memes and funny pictures I have accumulated and am sharing.';
$meta_keywords = '#humor, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Meme Collection',
  'tags' => 
  array (
    0 => '#humor',
  ),
  'excerpt' => 'This is a collection of internet memes and funny pictures I have accumulated and am sharing.',
  'source_file' => 'content\\gallery\\meme-collection.md',
);

// Raw content
$post_content = '<img src="coinbucket-carbatteries.png">


<img src="../img/yasqueen.jpg" alt="yasqueen.jpg"><img src="../img/boobs.jpg" alt="boobs.jpg">
<img src="../img/menstruacean.jpg" alt="menstruacean.jpg">

<img src="../img/justgo.png" alt="justgo.png">

<img src="../img/turkeybear.png" alt="turkeybear.png">
<img src="../img/30Weeks.jpg" alt="30Weeks.jpg">
<img src="../img/420.jpg" alt="420.jpg">
<img src="../img/254543_2022617478300_8036675_n.jpg" alt="254543_2022617478300_8036675_n.jpg">
<img src="../img/antiweedcouch.jpg" alt="antiweedcouch.jpg">
<img src="../img/apple-fact-water.jpg" alt="apple-fact-water.jpg">
<img src="../img/aroaceLifespan.png" alt="aroaceLifespan.png">
<img src="../img/badly-explaining-su-episodes-day-55-say-uncle-v0-ygtynrsde32a1.webp" alt="badly-explaining-su-episodes-day-55-say-uncle-v0-ygtynrsde32a1.webp">
<img src="../img/baron.png" alt="baron.png">
<img src="../img/biawareness.png" alt="biawareness.png">
<img src="../img/bigbutts.png" alt="bigbutts.png">
<img src="../img/bitchlake.png" alt="bitchlake.png">
<img src="../img/bluewilldo.jpg" alt="bluewilldo.jpg">
<img src="../img/bog.jpg" alt="bog.jpg">
<img src="../img/bunch-of-cheese.jpg" alt="bunch-of-cheese.jpg">
<img src="../img/burrito3am.png" alt="burrito3am.png">
<img src="../img/celery.png" alt="celery.png">
<img src="../img/changing lanes.jpg" alt="changing lanes.jpg">
<img src="../img/coinbucket-carbatteries.png" alt="coinbucket-carbatteries.png">
<img src="../img/companion.jpg" alt="companion.jpg">
<img src="../img/crispy-child.png" alt="crispy-child.png">
<img src="../img/cult.jpg" alt="cult.jpg">
<img src="../img/daddy.jpg" alt="daddy.jpg">
<img src="../img/demo.png" alt="demo.png">
<img src="../img/dino-nuggets.png" alt="dino-nuggets.png">
<img src="../img/dinosaurs.jpg" alt="dinosaurs.jpg">
<img src="../img/dog.jpg" alt="dog.jpg">
<img src="../img/dogPizzaSalad.png" alt="dogPizzaSalad.png">
<img src="../img/dogPoolParty.png" alt="dogPoolParty.png">
<img src="../img/dragwillConfuseKids.jpg" alt="dragwillConfuseKids.jpg">
<img src="../img/drink.jpg" alt="drink.jpg">
<img src="../img/dumbbitch.jpg" alt="dumbbitch.jpg">
<img src="../img/eaglemom.jpg" alt="eaglemom.jpg">
<img src="../img/easter.jpg" alt="easter.jpg">
<img src="../img/easterbasket.jpg" alt="easterbasket.jpg">
<img src="../img/finalsupper.png" alt="finalsupper.png">
<img src="../img/fishguard.png" alt="fishguard.png">
<img src="../img/flaminghot.png" alt="flaminghot.png">
<img src="../img/frat.jpg" alt="frat.jpg">
<img src="../img/fridge.png" alt="fridge.png">
<img src="../img/getinthebus.png" alt="getinthebus.png">
<img src="../img/girlsNight.png" alt="girlsNight.png">
<img src="../img/goldencorral.jpg" alt="goldencorral.jpg">
<img src="../img/goldilocks.png" alt="goldilocks.png">
<img src="../img/goodluck.jpg" alt="goodluck.jpg">
<img src="../img/goofygoobergooses.png" alt="goofygoobergooses.png">
<img src="../img/greatLakesMnemonic.jpg" alt="greatLakesMnemonic.jpg">
<img src="../img/guesshelldie.jpg" alt="guesshelldie.jpg">
<img src="../img/gunnagetweird.jpg" alt="gunnagetweird.jpg">
<img src="../img/hannibal.png" alt="hannibal.png">
<img src="../img/healthcareherojuice.jpg" alt="healthcareherojuice.jpg">
<img src="../img/heLovesmeNot.jpg" alt="heLovesmeNot.jpg">
<img src="../img/homealone.png" alt="homealone.png">
<img src="../img/hungryhippos.png" alt="hungryhippos.png">
<img src="../img/idfTortureMeme.jpg" alt="idfTortureMeme.jpg">
<img src="../img/iHitHim.png" alt="iHitHim.png">
<img src="../img/ilovedog.jpg" alt="ilovedog.jpg">
<img src="../img/imTheBaby.jpg" alt="imTheBaby.jpg">
<img src="../img/inthishouse.png" alt="inthishouse.png">
<img src="../img/james.jpg" alt="james.jpg">
<img src="../img/kathyrobbins.png" alt="kathyrobbins.png">
<img src="../img/koolaidthey.png" alt="koolaidthey.png">
<img src="../img/mambo5.jpg" alt="mambo5.jpg">
<img src="../img/niceshells.png" alt="niceshells.png">
<img src="../img/no-vember.png" alt="no-vember.png">
<img src="../img/noodles.jpg" alt="noodles.jpg">
<img src="../img/npcwholefamilydied.jpg" alt="npcwholefamilydied.jpg">
<img src="../img/oldManonLamb.jpg" alt="oldManonLamb.jpg">
<img src="../img/oncethebabyisborn.jpg" alt="oncethebabyisborn.jpg">
<img src="../img/parrot.jpg" alt="parrot.jpg">
<img src="../img/party.jpg" alt="party.jpg">
<img src="../img/pasteWater.png" alt="pasteWater.png">
<img src="../img/peach cobbler.jpg" alt="peach cobbler.jpg">
<img src="../img/personofscience.png" alt="personofscience.png">
<img src="../img/petLobster.png" alt="petLobster.png">
<img src="../img/phinneasSkull.jpg" alt="phinneasSkull.jpg">
<img src="../img/possum.png" alt="possum.png">
<img src="../img/possums.jpg" alt="possums.jpg">
<img src="../img/quokkamom.jpg" alt="quokkamom.jpg">
<img src="../img/R.png" alt="R.png">
<img src="../img/selfcare.jpg" alt="selfcare.jpg">
<img src="../img/seuss.png" alt="seuss.png">
<img src="../img/sindientes.jpg" alt="sindientes.jpg">
<img src="../img/stopdematerializing.png" alt="stopdematerializing.png">
<img src="../img/stupid-shit.png" alt="stupid-shit.png">
<img src="../img/sunshine.jpg" alt="sunshine.jpg">
<img src="../img/timeForCrab.jpg" alt="timeForCrab.jpg">
<img src="../img/toothpaste.jpg" alt="toothpaste.jpg">';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>