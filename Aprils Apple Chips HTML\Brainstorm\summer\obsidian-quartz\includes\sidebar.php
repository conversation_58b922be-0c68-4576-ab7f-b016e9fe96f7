<?php
// Ensure we have path constants available
if (!isset($paths) && isset($config)) {
    require_once ($base_url ?? '') . 'path-helper.php';
    $paths = initPaths($config, __FILE__);
}
?>
<section id="related">
    <!-- Breadcrumb Navigation -->
    <?php if (isset($breadcrumb) && !empty($breadcrumb)): ?>
    <div class="breadcrumb">
        <?php foreach ($breadcrumb as $index => $crumb): ?>
            <?php if ($index > 0): ?><span class="breadcrumb-separator">›</span><?php endif; ?>
            <?php if (isset($crumb['url'])): ?>
                <a href="<?php echo $crumb['url']; ?>"><?php echo htmlspecialchars($crumb['title']); ?></a>
            <?php else: ?>
                <span><?php echo htmlspecialchars($crumb['title']); ?></span>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>


    <div>
        <section id="about">
            <h3>About <?php echo htmlspecialchars($config['site']['name'] ?? 'A. A. Chips'); ?></h3>
            <p><?php echo htmlspecialchars($config['site']['description'] ?? 'Personal stories, advocacy work, and reflections on homelessness, family alienation, and rebuilding life. Join me on this journey of expression and connection.'); ?></p>
        </section>

        <section id="donate">
            <h3>Donate</h3>
            <p>If you like what I am doing. Or if you hate what I am doing. You can donate one-time or recurring pledges through my <a href="<?php echo $config['social']['kofi'] ?? 'https://www.ko-fi.com/aachips'; ?>">Ko-Fi page</a>.</p>
            <a href="<?php echo ($base_url ?? $paths['base_path'] ?? '') . 'crowdfund.html'; ?>" target="_blank">What am I raising funds for?</a>
        </section>

        <section id="categories">
            <h3>Content Categories</h3>
            <ul>
                <?php
                // Use categories from config, which now matches actual directories
                $categories = $config['categories'] ?? [];
                foreach ($categories as $category => $url):
                ?>
                    <li>
                        <a href="<?php echo ($base_url ?? $paths['base_path'] ?? '') . $url; ?>" class="internal-link"><?php echo htmlspecialchars($category); ?></a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </section>

        <section id="content-index">
            <h3>Quick Navigation</h3>
            <ul>
                <li><a href="<?php echo ($base_url ?? $paths['base_path'] ?? '') . 'content/contents.php'; ?>" class="internal-link">Site Contents</a></li>
                <li><a href="<?php echo ($base_url ?? $paths['base_path'] ?? '') . 'index2.php'; ?>" class="internal-link">Alternative Index</a></li>
                <li><a href="<?php echo ($base_url ?? $paths['base_path'] ?? '') . 'content/index.php'; ?>" class="internal-link">Main Index</a></li>
                <li><a href="<?php echo ($base_url ?? $paths['base_path'] ?? '') . 'content/humor/index.php'; ?>" class="internal-link">Humor Vault</a></li>
                <li><a href="<?php echo ($base_url ?? $paths['base_path'] ?? '') . 'content/inspiration/index.php'; ?>" class="internal-link">Inspiration Vault</a></li>
            </ul>
        </section>

        <section id="recent-posts">
            <h3>Recent Posts</h3>
            <ul>
                <?php
                // Get recent posts (this would be enhanced with actual recent post data)
                $recent_posts = [
                    ['title' => '200 Blunt Words', 'url' => 'content/alienation/200-blunt-words.php'],
                    ['title' => 'ADHD in Women', 'url' => 'content/street/adhd-in-women.php'],
                    ['title' => 'Alternatives to I Love You', 'url' => 'content/alienation/alternatives-to-iloveyou.php'],
                    ['title' => 'Hadestown Review', 'url' => 'content/hadestown-review.php'],
                    ['title' => 'Bite-Sized Learning', 'url' => 'content/bite-sized-learning.php']
                ];
                foreach ($recent_posts as $post):
                ?>
                    <li><a href="<?php echo ($base_url ?? $paths['base_path'] ?? '') . $post['url']; ?>" class="internal-link"><?php echo htmlspecialchars($post['title']); ?></a></li>
                <?php endforeach; ?>
            </ul>
        </section>

        <section id="random-posts">
            <h3>Random Posts</h3>
            <ul>
                <?php
                // Generate random posts from different categories
                $random_posts_list = [
                    ['title' => 'Manhunt for Ed Sheeran', 'url' => 'content/humor/manhunt-ed-sheeran.php'],
                    ['title' => 'Dans Story', 'url' => 'content/alienation/dans-story.php'],
                    ['title' => 'Finland Media Literacy', 'url' => 'content/inspiration/finland-media-literacy.php'],
                    ['title' => 'Hanukkahs Light', 'url' => 'content/judaism/hanukkahs-light.php'],
                    ['title' => 'Austin UBI', 'url' => 'content/street/austin-ubi.php'],
                    ['title' => 'Ecosattva Vows', 'url' => 'content/climate/ecosattva.php']
                ];

                // Shuffle and take first 4
                shuffle($random_posts_list);
                $displayed_random = array_slice($random_posts_list, 0, 4);

                foreach ($displayed_random as $post):
                ?>
                    <li><a href="<?php echo ($base_url ?? $paths['base_path'] ?? '') . $post['url']; ?>" class="internal-link"><?php echo htmlspecialchars($post['title']); ?></a></li>
                <?php endforeach; ?>
            </ul>
        </section>

        <section id="connect">
            <h3>Connect</h3>
            <p>Share your thoughts, questions, or just say hello. Use the chat widget below. Leave an email address to reply to.</p>
        </section>
    </div>
</section>
