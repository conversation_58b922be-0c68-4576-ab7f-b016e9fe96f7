<?php
/**
 * Local Development Setup for Visitor Counter
 * Creates a local database that matches your web server structure
 */

echo "<h1>🔧 Local Development Setup</h1>\n";
echo "<p>Setting up local database to match your web server structure...</p>\n";

// Step 1: Create the database
echo "<h2>Step 1: Creating Local Database</h2>\n";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS aachipsc_blog");
    echo "✅ Database 'aachipsc_blog' created/verified<br>\n";
    
    // Switch to the database
    $pdo->exec("USE aachipsc_blog");
    echo "✅ Switched to database 'aachipsc_blog'<br>\n";
    
} catch (PDOException $e) {
    echo "❌ <strong>FAILED:</strong> " . $e->getMessage() . "<br>\n";
    echo "💡 Make sure XAMPP MySQL is running<br>\n";
    exit;
}

// Step 2: Create visitor counter tables
echo "<h2>Step 2: Creating Visitor Counter Tables</h2>\n";
try {
    // Read the SQL file
    $sqlFile = __DIR__ . '/visitor-counter/simple_setup.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Split into individual statements and execute
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            
            // Extract table name for feedback
            if (preg_match('/CREATE TABLE.*?`?(\w+)`?\s*\(/i', $statement, $matches)) {
                echo "✅ Created table: {$matches[1]}<br>\n";
            } elseif (preg_match('/CREATE.*?PROCEDURE\s+(\w+)/i', $statement, $matches)) {
                echo "✅ Created procedure: {$matches[1]}<br>\n";
            } elseif (preg_match('/INSERT.*?INTO\s+(\w+)/i', $statement, $matches)) {
                echo "✅ Inserted data into: {$matches[1]}<br>\n";
            }
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') === false) {
                echo "⚠️ Warning: " . $e->getMessage() . "<br>\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ <strong>FAILED:</strong> " . $e->getMessage() . "<br>\n";
    exit;
}

// Step 3: Test the visitor counter system
echo "<h2>Step 3: Testing Visitor Counter System</h2>\n";
try {
    require_once 'comments/database.php';
    $db = CommentDatabase::getInstance();
    echo "✅ Database connection successful<br>\n";
    
    require_once 'visitor-counter/VisitorCounter.php';
    $counter = new VisitorCounter($db);
    
    // Test recording a visit
    $testSlug = 'local-dev-test-' . date('Y-m-d-H-i-s');
    $result = $counter->recordVisit($testSlug, 'Local Development Test');
    
    if ($result) {
        echo "✅ Visit recording successful<br>\n";
    } else {
        echo "⚠️ Visit recording returned false (might be duplicate)<br>\n";
    }
    
    // Test getting stats
    $stats = $counter->getPageStats($testSlug);
    if ($stats && $stats['total_visits'] >= 0) {
        echo "✅ Statistics retrieval successful<br>\n";
    } else {
        echo "❌ Statistics retrieval failed<br>\n";
    }
    
    // Clean up test data
    $pdo->prepare("DELETE FROM aachipsc_blog_page_visits WHERE page_slug = ?")->execute([$testSlug]);
    $pdo->prepare("DELETE FROM aachipsc_blog_page_stats WHERE page_slug = ?")->execute([$testSlug]);
    echo "✅ Test data cleaned up<br>\n";
    
} catch (Exception $e) {
    echo "❌ <strong>FAILED:</strong> " . $e->getMessage() . "<br>\n";
}

// Step 4: Show current tables
echo "<h2>Step 4: Verifying Database Structure</h2>\n";
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "📋 <strong>Tables in database:</strong><br>\n";
    foreach ($tables as $table) {
        echo "  • $table<br>\n";
    }
    
    // Check specifically for visitor counter tables
    $visitorTables = array_filter($tables, function($table) {
        return strpos($table, 'aachipsc_blog_') === 0;
    });
    
    echo "<br>🎯 <strong>Visitor counter tables:</strong><br>\n";
    foreach ($visitorTables as $table) {
        echo "  • $table<br>\n";
    }
    
} catch (PDOException $e) {
    echo "❌ <strong>FAILED:</strong> " . $e->getMessage() . "<br>\n";
}

echo "<h2>🎉 Setup Complete!</h2>\n";
echo "<div style='background: #e8f5e8; border: 1px solid #4caf50; border-radius: 6px; padding: 15px; margin: 20px 0;'>\n";
echo "<h3>✅ Local Development Environment Ready</h3>\n";
echo "<p>Your local database now matches your web server structure. You can:</p>\n";
echo "<ul>\n";
echo "<li><strong>Test locally:</strong> Visit <a href='test-visitor-counter.php'>test-visitor-counter.php</a></li>\n";
echo "<li><strong>View demo:</strong> Open <a href='visitor-counter-demo.html'>visitor-counter-demo.html</a></li>\n";
echo "<li><strong>Check footer:</strong> Look for visitor counters on any page</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<h2>🚀 Deploying to Web Server</h2>\n";
echo "<div style='background: #fff3cd; border: 1px solid #ffc107; border-radius: 6px; padding: 15px; margin: 20px 0;'>\n";
echo "<h3>📤 When ready to deploy:</h3>\n";
echo "<ol>\n";
echo "<li><strong>Upload files:</strong> Copy visitor-counter folder to your web server</li>\n";
echo "<li><strong>Update config:</strong> Change database credentials back to web server settings</li>\n";
echo "<li><strong>Import SQL:</strong> Run the SQL schema on your web server database</li>\n";
echo "<li><strong>Test live:</strong> Visit your live site to verify functionality</li>\n";
echo "</ol>\n";
echo "<p><strong>Web server credentials to use:</strong></p>\n";
echo "<code>\n";
echo "'host' => 'az1-ss110.a2hosting.com',<br>\n";
echo "'dbname' => 'aachipsc_blog',<br>\n";
echo "'username' => 'aachipsc_commentwriter',<br>\n";
echo "'password' => '\$gdIjGDkyFLX',<br>\n";
echo "</code>\n";
echo "</div>\n";

echo "<h2>🔧 File Structure</h2>\n";
echo "<p>Your setup now matches this structure:</p>\n";
echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>\n";
echo "obsidian-quartz/\n";
echo "├── visitor-counter/\n";
echo "│   ├── VisitorCounter.php\n";
echo "│   ├── visitor-display.php\n";
echo "│   ├── visitor_counter.sql\n";
echo "│   └── simple_setup.sql\n";
echo "├── css/\n";
echo "│   └── visitor-counter.css\n";
echo "├── includes/\n";
echo "│   └── footer.php (updated)\n";
echo "└── ../secure_config/\n";
echo "    └── obq_comments.php\n";
echo "</pre>\n";

echo "<style>\n";
echo "body { font-family: Arial, sans-serif; max-width: 900px; margin: 0 auto; padding: 20px; }\n";
echo "h1 { color: #333; border-bottom: 2px solid #007cba; }\n";
echo "h2 { color: #555; margin-top: 30px; }\n";
echo "code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }\n";
echo "pre { overflow-x: auto; }\n";
echo "</style>\n";
?>
