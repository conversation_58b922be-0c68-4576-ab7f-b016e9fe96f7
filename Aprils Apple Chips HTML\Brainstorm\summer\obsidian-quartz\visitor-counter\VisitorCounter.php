<?php
/**
 * Visitor Counter System
 * For A. A. Chips' Obsidian-Quartz Blog
 * 
 * Tracks page visits and site-wide statistics with proper deduplication
 * and performance optimization.
 */

class VisitorCounter {
    private $pdo;
    private $config;
    private $tablePrefix;
    
    public function __construct($database = null) {
        if ($database) {
            $this->pdo = $database->getPDO();
            $this->config = $database->getConfig();
            $this->tablePrefix = $database->getConfig('database.table_prefix') ?? 'aachipsc_blog_';
        } else {
            // Fallback to comments database if no specific database provided
            require_once __DIR__ . '/../comments/database.php';
            $db = CommentDatabase::getInstance();
            $this->pdo = $db->getPDO();
            $this->config = $db->getConfig();
            $this->tablePrefix = $db->getConfig('database.table_prefix') ?? 'aachipsc_blog_';
        }
    }
    
    /**
     * Record a page visit
     * 
     * @param string $pageSlug The page identifier (e.g., 'when-choice-isnt-choice')
     * @param string $pageTitle The human-readable page title
     * @param array $options Additional options (referrer, etc.)
     * @return bool Success status
     */
    public function recordVisit($pageSlug, $pageTitle = null, $options = []) {
        try {
            $ipAddress = $this->getClientIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $userAgentHash = hash('sha256', $userAgent);
            $sessionId = session_id() ?: null;
            $visitDate = date('Y-m-d');
            $referrer = $options['referrer'] ?? $_SERVER['HTTP_REFERER'] ?? null;
            
            // Check if this is a duplicate visit (same IP, user agent, page, and date)
            $isDuplicate = $this->isDuplicateVisit($pageSlug, $ipAddress, $userAgentHash, $visitDate);
            
            if (!$isDuplicate) {
                // Record the visit
                $this->insertVisit($pageSlug, $pageTitle, $ipAddress, $userAgentHash, $sessionId, $visitDate, $referrer);
                
                // Update aggregated statistics
                $this->updatePageStats($pageSlug, $pageTitle);
                $this->updateSiteStats();
                $this->updateVisitorSession($ipAddress, $userAgentHash, $sessionId, $pageSlug);
                
                return true;
            }
            
            return false; // Duplicate visit, not recorded
            
        } catch (Exception $e) {
            error_log("Visitor counter error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get visitor statistics for a specific page
     * 
     * @param string $pageSlug The page identifier
     * @return array Statistics array
     */
    public function getPageStats($pageSlug) {
        try {
            $table = $this->tablePrefix . 'page_stats';
            $sql = "SELECT * FROM {$table} WHERE page_slug = :page_slug";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute(['page_slug' => $pageSlug]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$stats) {
                // Return default stats if page not found
                return [
                    'page_slug' => $pageSlug,
                    'total_visits' => 0,
                    'unique_visits' => 0,
                    'today_visits' => 0,
                    'today_unique_visits' => 0,
                    'last_visit' => null,
                    'first_visit' => null
                ];
            }
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Error getting page stats: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get site-wide visitor statistics
     * 
     * @return array Site statistics
     */
    public function getSiteStats() {
        try {
            $table = $this->tablePrefix . 'site_stats';
            $sql = "SELECT stat_name, stat_value FROM {$table}";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $stats = [];
            foreach ($results as $row) {
                $stats[$row['stat_name']] = $row['stat_value'];
            }
            
            // Add today's stats
            $stats['today_total_visits'] = $this->getTodayTotalVisits();
            $stats['today_unique_visitors'] = $this->getTodayUniqueVisitors();
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Error getting site stats: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get formatted visitor count display
     * 
     * @param string $pageSlug The page identifier
     * @param array $options Display options
     * @return string HTML formatted visitor count
     */
    public function getVisitorCountDisplay($pageSlug, $options = []) {
        $pageStats = $this->getPageStats($pageSlug);
        $siteStats = $this->getSiteStats();
        
        $showPageCount = $options['show_page_count'] ?? true;
        $showSiteCount = $options['show_site_count'] ?? true;
        $showTodayCount = $options['show_today_count'] ?? false;
        $style = $options['style'] ?? 'retro';
        
        $html = '<div class="visitor-counter visitor-counter-' . $style . '">';
        
        if ($showPageCount && $pageStats) {
            $html .= '<div class="page-counter">';
            $html .= '<span class="counter-label">This page:</span> ';
            $html .= '<span class="counter-number">' . number_format($pageStats['total_visits']) . '</span> visits';
            if ($showTodayCount && $pageStats['today_visits'] > 0) {
                $html .= ' <span class="today-count">(' . $pageStats['today_visits'] . ' today)</span>';
            }
            $html .= '</div>';
        }
        
        if ($showSiteCount && !empty($siteStats)) {
            $html .= '<div class="site-counter">';
            $html .= '<span class="counter-label">Total site visits:</span> ';
            $html .= '<span class="counter-number">' . number_format($siteStats['total_site_visits'] ?? 0) . '</span>';
            if ($showTodayCount && isset($siteStats['today_total_visits']) && $siteStats['today_total_visits'] > 0) {
                $html .= ' <span class="today-count">(' . $siteStats['today_total_visits'] . ' today)</span>';
            }
            $html .= '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Check if this is a duplicate visit
     */
    private function isDuplicateVisit($pageSlug, $ipAddress, $userAgentHash, $visitDate) {
        $table = $this->tablePrefix . 'page_visits';
        $sql = "SELECT id FROM {$table} 
                WHERE page_slug = :page_slug 
                AND ip_address = :ip_address 
                AND user_agent_hash = :user_agent_hash 
                AND visit_date = :visit_date 
                LIMIT 1";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'page_slug' => $pageSlug,
            'ip_address' => $ipAddress,
            'user_agent_hash' => $userAgentHash,
            'visit_date' => $visitDate
        ]);
        
        return $stmt->fetch() !== false;
    }
    
    /**
     * Insert a new visit record
     */
    private function insertVisit($pageSlug, $pageTitle, $ipAddress, $userAgentHash, $sessionId, $visitDate, $referrer) {
        $table = $this->tablePrefix . 'page_visits';
        
        // Check if this is a unique visitor (first time ever)
        $isUniqueTotal = $this->isUniqueVisitor($pageSlug, $ipAddress, $userAgentHash);
        
        $sql = "INSERT INTO {$table} 
                (page_slug, page_title, ip_address, user_agent_hash, session_id, visit_date, 
                 is_unique_daily, is_unique_total, referrer) 
                VALUES 
                (:page_slug, :page_title, :ip_address, :user_agent_hash, :session_id, :visit_date, 
                 TRUE, :is_unique_total, :referrer)";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'page_slug' => $pageSlug,
            'page_title' => $pageTitle,
            'ip_address' => $ipAddress,
            'user_agent_hash' => $userAgentHash,
            'session_id' => $sessionId,
            'visit_date' => $visitDate,
            'is_unique_total' => $isUniqueTotal,
            'referrer' => $referrer
        ]);
    }
    
    /**
     * Check if this is a unique visitor for this page
     */
    private function isUniqueVisitor($pageSlug, $ipAddress, $userAgentHash) {
        $table = $this->tablePrefix . 'page_visits';
        $sql = "SELECT id FROM {$table} 
                WHERE page_slug = :page_slug 
                AND ip_address = :ip_address 
                AND user_agent_hash = :user_agent_hash 
                LIMIT 1";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            'page_slug' => $pageSlug,
            'ip_address' => $ipAddress,
            'user_agent_hash' => $userAgentHash
        ]);
        
        return $stmt->fetch() === false;
    }
    
    /**
     * Update page statistics using stored procedure
     */
    private function updatePageStats($pageSlug, $pageTitle) {
        try {
            $sql = "CALL UpdatePageStats(:page_slug)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute(['page_slug' => $pageSlug]);
            
            // Update page title if provided
            if ($pageTitle) {
                $table = $this->tablePrefix . 'page_stats';
                $sql = "UPDATE {$table} SET page_title = :page_title WHERE page_slug = :page_slug";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute(['page_title' => $pageTitle, 'page_slug' => $pageSlug]);
            }
        } catch (Exception $e) {
            error_log("Error updating page stats: " . $e->getMessage());
        }
    }
    
    /**
     * Update site-wide statistics
     */
    private function updateSiteStats() {
        try {
            $table = $this->tablePrefix . 'site_stats';
            
            // Update total site visits
            $sql = "UPDATE {$table} SET stat_value = stat_value + 1 WHERE stat_name = 'total_site_visits'";
            $this->pdo->exec($sql);
            
            // Update total pages visited count
            $pageStatsTable = $this->tablePrefix . 'page_stats';
            $sql = "UPDATE {$table} SET stat_value = (
                        SELECT COUNT(*) FROM {$pageStatsTable}
                    ) WHERE stat_name = 'total_pages_visited'";
            $this->pdo->exec($sql);
            
        } catch (Exception $e) {
            error_log("Error updating site stats: " . $e->getMessage());
        }
    }
    
    /**
     * Update visitor session information
     */
    private function updateVisitorSession($ipAddress, $userAgentHash, $sessionId, $pageSlug) {
        try {
            $table = $this->tablePrefix . 'visitor_sessions';
            
            $sql = "INSERT INTO {$table} (ip_address, user_agent_hash, session_id, total_page_views, pages_visited)
                    VALUES (:ip_address, :user_agent_hash, :session_id, 1, :pages_visited)
                    ON DUPLICATE KEY UPDATE
                    last_visit = CURRENT_TIMESTAMP,
                    total_page_views = total_page_views + 1,
                    pages_visited = CONCAT(COALESCE(pages_visited, ''), ',', :pages_visited)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                'ip_address' => $ipAddress,
                'user_agent_hash' => $userAgentHash,
                'session_id' => $sessionId,
                'pages_visited' => $pageSlug
            ]);
            
        } catch (Exception $e) {
            error_log("Error updating visitor session: " . $e->getMessage());
        }
    }
    
    /**
     * Get today's total visits across all pages
     */
    private function getTodayTotalVisits() {
        $table = $this->tablePrefix . 'page_visits';
        $sql = "SELECT COUNT(*) FROM {$table} WHERE visit_date = CURDATE()";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();
        return $stmt->fetchColumn();
    }
    
    /**
     * Get today's unique visitors across all pages
     */
    private function getTodayUniqueVisitors() {
        $table = $this->tablePrefix . 'page_visits';
        $sql = "SELECT COUNT(DISTINCT CONCAT(ip_address, user_agent_hash)) FROM {$table} WHERE visit_date = CURDATE()";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();
        return $stmt->fetchColumn();
    }
    
    /**
     * Get client IP address (handles proxies and load balancers)
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 
                   'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}
