/**
 * Comments System JavaScript
 * For A. A. Chips' Obsidian-Quartz Blog
 */

class CommentsSystem {
    constructor(postSlug) {
        this.postSlug = postSlug;
        this.currentPage = 1;
        this.isLoading = false;
        this.currentUser = null;
        
        // DOM elements
        this.form = document.getElementById('comment-form');
        this.contentTextarea = document.getElementById('comment-content');
        this.charCount = document.getElementById('char-count');
        this.commentsList = document.getElementById('comments-list');
        this.loadingIndicator = document.getElementById('comments-loading');
        this.loadMoreButton = document.getElementById('load-more-comments');
        this.loadMoreContainer = document.getElementById('load-more-container');
        this.commentCount = document.getElementById('comment-count');
        this.cancelReplyButton = document.getElementById('cancel-reply');
    }

    async init() {
        await this.getCurrentUser();
        this.setupEventListeners();
        await this.loadComments();
    }

    async getCurrentUser() {
        try {
            const response = await fetch('comments/comment-handler.php?action=user');
            const result = await response.json();
            if (result.success) {
                this.currentUser = result.data.user;
            }
        } catch (error) {
            console.error('Error getting current user:', error);
        }
    }

    setupEventListeners() {
        // Comment form submission
        if (this.form) {
            this.form.addEventListener('submit', (e) => this.handleCommentSubmit(e));
        }

        // Character count
        if (this.contentTextarea && this.charCount) {
            this.contentTextarea.addEventListener('input', () => {
                this.charCount.textContent = this.contentTextarea.value.length;
            });
        }

        // Load more comments
        if (this.loadMoreButton) {
            this.loadMoreButton.addEventListener('click', () => this.loadMoreComments());
        }

        // Cancel reply
        if (this.cancelReplyButton) {
            this.cancelReplyButton.addEventListener('click', () => this.cancelReply());
        }

        // Change user button
        const changeUserButton = document.getElementById('change-user');
        if (changeUserButton) {
            changeUserButton.addEventListener('click', () => this.changeUser());
        }
    }

    async handleCommentSubmit(e) {
        e.preventDefault();

        if (this.isLoading) return;

        const formData = new FormData(this.form);
        const content = formData.get('content').trim();
        const name = formData.get('name').trim();
        const email = formData.get('email').trim();

        if (!content) {
            this.showError('Please enter a comment');
            return;
        }

        if (!name) {
            this.showError('Please enter your name');
            return;
        }

        if (!email) {
            this.showError('Please enter your email');
            return;
        }

        this.isLoading = true;
        this.setFormLoading(true);

        try {
            const response = await fetch('comments/comment-handler.php?action=comment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    post_slug: this.postSlug,
                    content: content,
                    name: name,
                    email: email,
                    parent_id: formData.get('parent_id') || null,
                    [formData.get('honeypot_field')]: formData.get('honeypot_value') || ''
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('Comment posted successfully!');
                this.form.reset();
                this.charCount.textContent = '0';
                this.cancelReply();
                await this.refreshComments();
            } else {
                this.showError(result.error || 'Failed to post comment');
            }
        } catch (error) {
            console.error('Error posting comment:', error);
            this.showError('Network error. Please try again.');
        } finally {
            this.isLoading = false;
            this.setFormLoading(false);
        }
    }

    async loadComments(page = 1) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.loadingIndicator.style.display = 'block';

        try {
            const response = await fetch(`comments/comment-handler.php?action=comments&post_slug=${encodeURIComponent(this.postSlug)}&page=${page}`);
            const result = await response.json();
            
            if (result.success) {
                const { comments, total_comments, has_more } = result.data;
                
                if (page === 1) {
                    this.commentsList.innerHTML = '';
                }
                
                comments.forEach(comment => {
                    this.renderComment(comment);
                });
                
                this.commentCount.textContent = total_comments;
                this.loadMoreContainer.style.display = has_more ? 'block' : 'none';
                this.currentPage = page;
            } else {
                this.showError(result.error || 'Failed to load comments');
            }
        } catch (error) {
            console.error('Error loading comments:', error);
            this.showError('Failed to load comments');
        } finally {
            this.isLoading = false;
            this.loadingIndicator.style.display = 'none';
        }
    }

    async loadMoreComments() {
        await this.loadComments(this.currentPage + 1);
    }

    async refreshComments() {
        this.currentPage = 1;
        await this.loadComments(1);
    }

    renderComment(comment, isReply = false) {
        const commentElement = document.createElement('div');
        commentElement.className = `comment ${isReply ? 'comment-reply' : ''}`;
        commentElement.dataset.commentId = comment.id;
        
        const userVote = comment.user_vote || null;
        const canVote = this.currentUser !== null;
        const canReply = this.currentUser !== null && !isReply; // Only allow one level of replies
        
        commentElement.innerHTML = `
            <div class="comment-header">
                <div class="comment-avatar">
                    ${this.escapeHtml(comment.name).charAt(0).toUpperCase()}
                </div>
                <div class="comment-meta">
                    <span class="comment-author">${this.escapeHtml(comment.name)}</span>
                    <span class="comment-date">${this.formatDate(comment.created_at)}</span>
                </div>
            </div>
            <div class="comment-content">
                ${this.escapeHtml(comment.content).replace(/\n/g, '<br>')}
            </div>
            <div class="comment-actions">
                ${canVote ? `
                    <button class="vote-btn ${userVote === 'like' ? 'active' : ''}" 
                            data-vote="like" data-comment-id="${comment.id}">
                        👍 <span class="vote-count">${comment.likes || 0}</span>
                    </button>
                    <button class="vote-btn ${userVote === 'dislike' ? 'active' : ''}" 
                            data-vote="dislike" data-comment-id="${comment.id}">
                        👎 <span class="vote-count">${comment.dislikes || 0}</span>
                    </button>
                ` : `
                    <span class="vote-display">👍 ${comment.likes || 0}</span>
                    <span class="vote-display">👎 ${comment.dislikes || 0}</span>
                `}
                ${canReply ? `
                    <button class="reply-btn" data-comment-id="${comment.id}" data-author="${this.escapeHtml(comment.name)}">
                        Reply
                    </button>
                ` : ''}
            </div>
            <div class="comment-replies">
                ${comment.replies ? comment.replies.map(reply => this.renderComment(reply, true)).join('') : ''}
            </div>
        `;
        
        // Add event listeners for voting and replying
        if (canVote) {
            commentElement.querySelectorAll('.vote-btn').forEach(btn => {
                btn.addEventListener('click', (e) => this.handleVote(e));
            });
        }
        
        if (canReply) {
            const replyBtn = commentElement.querySelector('.reply-btn');
            if (replyBtn) {
                replyBtn.addEventListener('click', (e) => this.handleReply(e));
            }
        }
        
        if (isReply) {
            return commentElement.outerHTML;
        } else {
            this.commentsList.appendChild(commentElement);
        }
    }

    async handleVote(e) {
        e.preventDefault();
        
        if (!this.currentUser) {
            this.showError('Please sign in to vote');
            return;
        }
        
        const button = e.target.closest('.vote-btn');
        const commentId = parseInt(button.dataset.commentId);
        const voteType = button.dataset.vote;
        const isActive = button.classList.contains('active');
        
        // Determine the action: if already voted this way, remove vote; otherwise, vote
        const action = isActive ? 'remove' : voteType;
        
        try {
            const response = await fetch('comments/comment-handler.php?action=vote', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    comment_id: commentId,
                    vote_type: action
                })
            });

            const result = await response.json();
            
            if (result.success) {
                // Refresh comments to show updated vote counts
                await this.refreshComments();
            } else {
                this.showError(result.error || 'Failed to record vote');
            }
        } catch (error) {
            console.error('Error voting:', error);
            this.showError('Network error. Please try again.');
        }
    }

    handleReply(e) {
        e.preventDefault();
        
        const button = e.target.closest('.reply-btn');
        const commentId = button.dataset.commentId;
        const authorName = button.dataset.author;
        
        // Set the parent ID in the form
        const parentIdInput = this.form.querySelector('input[name="parent_id"]');
        parentIdInput.value = commentId;
        
        // Update the textarea placeholder
        this.contentTextarea.placeholder = `Replying to ${authorName}...`;
        
        // Show cancel reply button
        this.cancelReplyButton.style.display = 'inline-block';
        
        // Focus the textarea
        this.contentTextarea.focus();
        
        // Scroll to the form
        this.form.scrollIntoView({ behavior: 'smooth' });
    }

    cancelReply() {
        const parentIdInput = this.form.querySelector('input[name="parent_id"]');
        parentIdInput.value = '';

        this.contentTextarea.placeholder = 'Share your thoughts...';
        this.cancelReplyButton.style.display = 'none';
    }

    changeUser() {
        // Clear session and reload page to show name/email fields
        fetch('comments/simple-auth.php?action=logout', { method: 'POST' })
            .then(() => {
                location.reload();
            })
            .catch(() => {
                location.reload(); // Reload anyway
            });
    }

    setFormLoading(loading) {
        const submitButton = this.form.querySelector('button[type="submit"]');
        if (loading) {
            submitButton.disabled = true;
            submitButton.textContent = 'Posting...';
        } else {
            submitButton.disabled = false;
            submitButton.textContent = 'Post Comment';
        }
    }

    showError(message) {
        this.showMessage(message, 'error');
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showMessage(message, type) {
        // Create or update message element
        let messageEl = document.getElementById('comment-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.id = 'comment-message';
            this.form.parentNode.insertBefore(messageEl, this.form);
        }
        
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        messageEl.style.display = 'block';
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            messageEl.style.display = 'none';
        }, 5000);
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}
