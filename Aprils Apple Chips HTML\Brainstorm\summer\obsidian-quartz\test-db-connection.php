<?php
/**
 * Database Connection Test
 * Simple script to test database connectivity before setting up visitor counter
 */

echo "<h1>Database Connection Test</h1>\n";
echo "<p>Testing connection to your database...</p>\n";

// Test 1: Basic PDO connection with default XAMPP settings
echo "<h2>Test 1: Default XAMPP Connection</h2>\n";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=aachipsc", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ <strong>SUCCESS:</strong> Connected to database 'aachipsc' with default XAMPP settings<br>\n";
    
    // Test if we can query the database
    $stmt = $pdo->query("SELECT DATABASE() as current_db, VERSION() as mysql_version");
    $result = $stmt->fetch();
    echo "📊 Current database: <strong>{$result['current_db']}</strong><br>\n";
    echo "🔧 MySQL version: <strong>{$result['mysql_version']}</strong><br>\n";
    
} catch (PDOException $e) {
    echo "❌ <strong>FAILED:</strong> " . $e->getMessage() . "<br>\n";
    echo "💡 <strong>Suggestion:</strong> Make sure MySQL is running in XAMPP<br>\n";
}

// Test 2: Check if database exists
echo "<h2>Test 2: Database Existence</h2>\n";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SHOW DATABASES LIKE 'aachipsc'");
    if ($stmt->fetch()) {
        echo "✅ <strong>SUCCESS:</strong> Database 'aachipsc' exists<br>\n";
    } else {
        echo "❌ <strong>FAILED:</strong> Database 'aachipsc' does not exist<br>\n";
        echo "💡 <strong>Suggestion:</strong> Create the database first:<br>\n";
        echo "<code>CREATE DATABASE aachipsc;</code><br>\n";
    }
} catch (PDOException $e) {
    echo "❌ <strong>FAILED:</strong> " . $e->getMessage() . "<br>\n";
}

// Test 3: Check existing tables
echo "<h2>Test 3: Existing Tables</h2>\n";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=aachipsc", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "ℹ️ <strong>INFO:</strong> No tables found in database<br>\n";
    } else {
        echo "📋 <strong>Existing tables:</strong><br>\n";
        foreach ($tables as $table) {
            echo "  • $table<br>\n";
        }
    }
    
    // Check for visitor counter tables specifically
    $visitorTables = array_filter($tables, function($table) {
        return strpos($table, 'aachipsc_blog_') === 0;
    });
    
    if (empty($visitorTables)) {
        echo "ℹ️ <strong>INFO:</strong> No visitor counter tables found (this is expected for new setup)<br>\n";
    } else {
        echo "🎯 <strong>Visitor counter tables found:</strong><br>\n";
        foreach ($visitorTables as $table) {
            echo "  • $table<br>\n";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ <strong>FAILED:</strong> " . $e->getMessage() . "<br>\n";
}

// Test 4: Test using the comments system configuration
echo "<h2>Test 4: Comments System Configuration</h2>\n";
try {
    require_once 'comments/database.php';
    $db = CommentDatabase::getInstance();
    $pdo = $db->getPDO();
    echo "✅ <strong>SUCCESS:</strong> Comments database connection working<br>\n";
    
    $config = $db->getConfig();
    echo "📊 Database name: <strong>{$config['database']['dbname']}</strong><br>\n";
    echo "🏷️ Table prefix: <strong>{$config['database']['table_prefix']}</strong><br>\n";
    
} catch (Exception $e) {
    echo "❌ <strong>FAILED:</strong> " . $e->getMessage() . "<br>\n";
    echo "💡 <strong>Suggestion:</strong> Check your secure config file<br>\n";
}

echo "<h2>📋 Next Steps</h2>\n";
echo "<p>If all tests passed, you can proceed with the visitor counter setup:</p>\n";
echo "<ol>\n";
echo "<li><strong>Import SQL Schema:</strong> Import <code>visitor-counter/visitor_counter.sql</code> into your database</li>\n";
echo "<li><strong>Test Visitor Counter:</strong> Visit <code>test-visitor-counter.php</code></li>\n";
echo "<li><strong>Check Footer:</strong> Look for visitor counter in page footers</li>\n";
echo "</ol>\n";

echo "<h2>🛠️ Manual SQL Import</h2>\n";
echo "<p>If you need to manually import the SQL schema:</p>\n";
echo "<ol>\n";
echo "<li>Open phpMyAdmin (usually <code>localhost/phpmyadmin</code>)</li>\n";
echo "<li>Select the <strong>aachipsc</strong> database</li>\n";
echo "<li>Click <strong>Import</strong> tab</li>\n";
echo "<li>Choose file: <code>visitor-counter/visitor_counter.sql</code></li>\n";
echo "<li>Click <strong>Go</strong></li>\n";
echo "</ol>\n";

echo "<h2>🔧 Alternative: Create Database Manually</h2>\n";
if (isset($pdo)) {
    echo "<p>If the database doesn't exist, you can create it by running this SQL:</p>\n";
    echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace;'>\n";
    echo "CREATE DATABASE IF NOT EXISTS aachipsc;<br>\n";
    echo "USE aachipsc;<br>\n";
    echo "</div>\n";
}

echo "<style>\n";
echo "body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }\n";
echo "h1 { color: #333; border-bottom: 2px solid #007cba; }\n";
echo "h2 { color: #555; margin-top: 30px; }\n";
echo "code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }\n";
echo "</style>\n";
?>
