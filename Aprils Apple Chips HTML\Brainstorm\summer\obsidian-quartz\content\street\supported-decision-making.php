<?php
// Auto-generated blog post
// Source: supported-decision-making.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Until you\'ve lost the ability to make rational decisions, you retain the ability to make dumb ones.';
$meta_description = 'Until you\'ve lost the ability to make rational decisions, you retain the ability to make dumb ones.';
$meta_keywords = 'homeless, advocacy, alienation, accessibility, sdm, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/donuts.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Until you\'ve lost the ability to make rational decisions, you retain the ability to make dumb ones.',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'tags' => 
  array (
    0 => 'homeless',
    1 => 'advocacy',
    2 => 'alienation',
    3 => 'accessibility',
    4 => 'sdm',
  ),
  'categories' => 
  array (
    0 => 'Alienation',
    1 => 'Street Advocacy',
    2 => 'Personal Stories',
    3 => 'Journal',
  ),
  'excerpt' => 'Until you\'ve lost the ability to make rational decisions, you retain the ability to make dumb ones.',
  'thumbnail' => '../../img/donuts.jpg',
  'source_file' => 'content\\street\\supported-decision-making.md',
);

// Raw content
$post_content = '<h3>Understanding Supported Decision Making</h3>

<p>It can be concerning to witness someone in your life making what appear to be consistently poor choices, whether it\'s related to their health, finances, or even parenting. You might feel a strong urge for them to seek professional help. However, unless a court deems an individual a vulnerable adult requiring a guardian or conservator, they retain their full legal rights, including the right to make decisions others may disagree with and to refuse assistance.</p>

<p>Guardianship and conservatorship involve a significant loss of rights and are difficult to reverse, making them options to be considered with extreme caution. Historically rooted in feudal law, these systems can unfortunately leave individuals vulnerable to exploitation. While the state can act as a \'Guardian of Last Resort\' in certain legal situations, a less restrictive and more empowering alternative exists: Supported Decision Making.</p>

<p>Supported Decision Making recognizes the fundamental right of every individual to seek counsel and involve trusted people in their affairs. This approach shifts away from the outdated notion of people with disabilities being incapable of making their own choices. Instead, it acknowledges the individual as the expert on their own life and allows them to choose trustworthy helpers for guidance. As long as decisions do not harm themselves or others (or break laws), individuals have the right to make choices, even if those choices are not universally understood or agreed upon. This contrasts sharply with historical systems that have treated individuals with disabilities as less capable, akin to children needing to be controlled.</p>

<p>Supported Decision Making is a human right. Everyone deserves to have support in making life-impacting decisions.</p>

<h3>Supported Decision Making: A Fundamental Human Right</h3>

<p>The traditional approach to decision-making for individuals with disabilities has often been rooted in a guardianship or conservatorship model, which assumes incapacity and the need for others to take control. This outdated perspective has perpetuated harmful stereotypes and denied individuals their basic right to autonomy.</p>

<p>Supported Decision Making (SDM) offers a transformative alternative. It acknowledges that each person, regardless of disability, is the primary authority on their own life. SDM empowers individuals to make their own choices with the assistance of trusted supporters who provide guidance and help them grasp the implications of their decisions.</p>

<h3>Why is Supported Decision Making Important?</h3>

<ul><li>It\'s a human right: The ability to make choices about one\'s own life is a fundamental right. SDM upholds this right, fostering self-determination and dignity.</li>

<p><li>It challenges stereotypes: SDM refutes the damaging idea that individuals with disabilities cannot make informed choices. It recognizes their agency and potential for meaningful participation in their lives.</li></p>

<p><li>It fosters independence: By offering support rather than taking over, SDM encourages individuals to develop their decision-making skills and become more self-reliant.</li></p>

<h3>How Does Supported Decision Making Work?</h3>

<p>SDM involves a collaborative process where the individual identifies trusted supporters who can:</p>

<p><li>Provide information: Supporters assist the individual in gathering and understanding relevant information for their choices.</li></p>

<p><li>Explore options: Supporters help in examining different choices and their potential outcomes.</li></p>

<p><li>Communicate preferences: Supporters aid the individual in clearly expressing their wishes and decisions.</li></p>

<p><li>Access resources: Supporters connect the individual with resources and services that can support their choices.</li></ul></p>

<h3>The Role of Professionals</h3>

<p>For professionals in human services, championing Supported Decision Making is a crucial responsibility. We must affirm our clients\' autonomy and self-determination, actively working to dismantle the outdated guardianship system that often strips individuals of their rights.</p>

<h3>Learn More</h3>

<p>Consider exploring the lecture and four-part video series on Supported Decision Making by UC Davis Minds Institute. Jonathan Martini, a lawyer and compelling advocate, passionately argues for our fundamental right to choose and presents evidence supporting the shift from guardianship to SDM.</p>

<iframe width="560" height="315" src="https://www.youtube.com/embed/Q8Na88Wz90I?si=kXdJGii521jfleV8" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

<p>Let\'s embrace Supported Decision Making and empower individuals to lead self-directed lives.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>