<?php
// Auto-generated blog post
// Source: humor-vault.md

// Load configuration
$config = include '../../config.php';

// Page variables
$page_title = 'Humor Vault - A. A. Chips';
$meta_description = 'Here\'s a bunch of funny things I\'ve saved and bookmarked off the internet.';
$meta_keywords = 'aachips, april, humor, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../../css/';
$js_path = '../../js/';
$base_url = '../../';
$related_posts = [
    ['title' => 'Alienation', 'url' => '../../alienation/index.php', 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => '../../climate/index.php', 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => '../../humor/index.php', 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => '../../inspiration/index.php', 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => '../../journal/index.php', 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => '../../judaism/index.php', 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => '../../kitchen/index.php', 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Street', 'url' => '../../street/index.php', 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => '../../writings/index.php', 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Humor Vault - A. A. Chips',
  'author' => 'many',
  'excerpt' => 'Here\'s a bunch of funny things I\'ve saved and bookmarked off the internet.',
  'tags' => 
  array (
    0 => 'aachips',
    1 => 'april',
    2 => 'humor',
  ),
  'source_file' => 'content\\humor\\humor-vault.md',
);

// Raw content
$post_content = '<p>Here is some funny things I\'ve collected from the internet. I am sharing these in good faith that the creators are okay with their content being shared and celebrated. Original channels are linked in each page if it exists.</p>

<ul><li><a href="alanis-narrates-your-day.php" class="internal-link">alanis-narrates-your-day</a></li>
<p><li><a href="bp-coffee.php" class="internal-link">bp-coffee</a></li></p>
<p><li><a href="friends-all-wrong.php" class="internal-link">friends-all-wrong</a></li></p>
<p><li><a href="you-died-youtube-short.php" class="internal-link">You Died - Youtube Short</a></li></p>
<p><li><a href="learn-attack.php" class="internal-link">learn-attack</a></li></p>
<p><li><a href="libertarian-pd.php" class="internal-link">libertarian-pd</a></li></p>
<p><li><a href="manhunt-ed-sheeran.php" class="internal-link">manhunt-ed-sheeran</a></li></p>
<p><li><a href="theonion-ten-weirdest-people.php" class="internal-link">theonion-ten-weirdest-people</a></li></p>
<p><li><a href="cant-believe-hes-gone.php" class="internal-link">cant-believe-hes-gone</a></li></p>
<p><li><a href="db-bros-save-day.php" class="internal-link">db-bros-save-day</a></li></p>
<p><li><a href="where-nothing-bad-happens.php" class="internal-link">where-nothing-bad-happens</a></li></p>
<p><li><a href="meme-collection.php" class="internal-link">meme-collection</a></li></p>
<p><li><a href="tp-bears.php" class="internal-link">tp-bears</a></li></p>
<p><li><a href="visit-titanic.php" class="internal-link">visit-titanic</a></li></ul></p>


';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../../page template.htm';
?>