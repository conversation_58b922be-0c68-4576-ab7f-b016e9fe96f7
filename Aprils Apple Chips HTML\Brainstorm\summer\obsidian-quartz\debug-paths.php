<?php
// Debug path resolution from category directory
echo "Original working directory: " . getcwd() . PHP_EOL;
chdir('content/alienation');
echo "Changed to: " . getcwd() . PHP_EOL;

// Test the actual paths that would be used in the category index
require_once '../../path-helper.php';
$config = include '../../config.php';
$paths = initPaths($config, getcwd() . '/index.php');

echo "Current working directory: " . getcwd() . PHP_EOL;
echo "Template path: " . $paths['template_path'] . PHP_EOL;
echo "Template file exists: " . (file_exists($paths['template_path']) ? 'YES' : 'NO') . PHP_EOL;
echo "CSS path: " . $paths['css_path'] . PHP_EOL;
echo "JS path: " . $paths['js_path'] . PHP_EOL;
echo "Base path: " . $paths['base_path'] . PHP_EOL;
echo "Current depth: " . $paths['current_depth'] . PHP_EOL;

// Test if template exists with different paths
echo "\nTesting template paths:\n";
echo "../../page template.htm exists: " . (file_exists('../../page template.htm') ? 'YES' : 'NO') . PHP_EOL;
echo "__DIR__ . '/../../page template.htm' exists: " . (file_exists(__DIR__ . '/../../page template.htm') ? 'YES' : 'NO') . PHP_EOL;
?>
