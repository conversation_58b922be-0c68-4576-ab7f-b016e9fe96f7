<?php
// Auto-generated blog post
// Source: last-night-before-they-leave.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The Last Night Before They Leave';
$meta_description = 'Tonight is a mess of burnt edges and bleach fumes, of paranoia and misplaced concern. But it’s also the last night, and somehow, in its own chaotic way, it feels like a goodbye.';
$meta_keywords = 'journal, homeless, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The Last Night Before They Leave',
  'author' => 'A. A. Chips',
  'excerpt' => 'Tonight is a mess of burnt edges and bleach fumes, of paranoia and misplaced concern. But it’s also the last night, and somehow, in its own chaotic way, it feels like a goodbye.',
  'date' => '2025-02-26',
  'categories' => 
  array (
    0 => 'Journal',
  ),
  'tags' => 
  array (
    0 => 'journal',
    1 => 'homeless',
  ),
  'source_file' => 'content\\journal\\last-night-before-they-leave.md',
);

// Raw content
$post_content = '<p><strong>“The Last Night Before They Leave”</strong></p>

<p>I got home from work today, exhausted after being up since 6:30, and all I wanted was a nap. But of course, they were in the kitchen, cooking. Cooking is always an event with them—usually ending in smoke alarms and me scrambling to air out the house. I’ve tried to teach them: low and slow, oil the pan, or just use the toaster oven at 350. Safe, foolproof. Tonight, they actually listened. The smell of pork filled the air, and for a moment, I felt like a cartoon character floating on the scent of something delicious.</p>

<p>Then came the knock on my door. “I think there’s tapeworms in the meat,” they said, their voice tinged with panic. The pork, still scalding hot, was already in the trash. I tried to explain—gamey meat can have parasites, that’s why you freeze it—but reasoning doesn’t work well in moments like these. They were scared, not just for themselves but for the cat, too. They spilled water while trying to clean, then fretted about sanitizing the floor.</p>

<p>It’s funny, in a way. They’ve never been one to help with cleaning—the kitchen is my domain, messy but clean—yet here they were, lecturing me about sanitation. Still, I handed them the Clorox wipes. Now, they’re on their third shower, and it sounds like someone’s being waterboarded in the bathroom. I’m just hoping there’s no mess to deal with later.</p>

<p>As chaotic as it all is, I’m choosing to see this as their strange, clumsy way of showing care. They’re leaving tomorrow, and maybe this is the closest thing to love they know how to express.</p>

<p>At one point, they yelled, “Stop attacking me!”—hard to nap through that. “Tweakers are really annoying,” I thought, though I don’t think it’s stimulants this time. Street life taught them to stay awake to stay safe, but now it feels more like alcohol or even just too much caffeine.</p>

<p>Tonight is a mess of burnt edges and bleach fumes, of paranoia and misplaced concern. But it’s also the last night, and somehow, in its own chaotic way, it feels like a goodbye.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>