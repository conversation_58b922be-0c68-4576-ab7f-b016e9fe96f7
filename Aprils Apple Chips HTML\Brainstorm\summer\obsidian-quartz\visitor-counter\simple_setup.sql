-- Simple Visitor Counter Setup
-- Copy and paste this into php<PERSON>y<PERSON><PERSON><PERSON> or your MySQL client

-- Create the main visitor tracking table
CREATE TABLE IF NOT EXISTS aachipsc_blog_page_visits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_slug VARCHAR(255) NOT NULL,
    page_title VARCHAR(500) DEFAULT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent_hash VARCHAR(64) NOT NULL,
    visit_date DATE NOT NULL,
    visit_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_page_slug (page_slug),
    INDEX idx_ip_address (ip_address),
    INDEX idx_visit_date (visit_date),
    UNIQUE KEY unique_daily_visit (page_slug, ip_address, user_agent_hash, visit_date)
);

-- Create the page statistics table
CREATE TABLE IF NOT EXISTS aachipsc_blog_page_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_slug VARCHAR(255) NOT NULL UNIQUE,
    page_title VARCHAR(500) DEFAULT NULL,
    total_visits INT DEFAULT 0,
    unique_visits INT DEFAULT 0,
    today_visits INT DEFAULT 0,
    last_visit TIMESTAMP DEFAULT NULL,
    first_visit TIMESTAMP DEFAULT NULL,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_page_slug (page_slug)
);

-- Create the site-wide statistics table
CREATE TABLE IF NOT EXISTS aachipsc_blog_site_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stat_name VARCHAR(100) NOT NULL UNIQUE,
    stat_value BIGINT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert initial site statistics
INSERT IGNORE INTO aachipsc_blog_site_stats (stat_name, stat_value) VALUES
('total_site_visits', 0),
('unique_site_visitors', 0),
('site_launch_date', UNIX_TIMESTAMP(NOW()));

-- Create visitor sessions table
CREATE TABLE IF NOT EXISTS aachipsc_blog_visitor_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    user_agent_hash VARCHAR(64) NOT NULL,
    first_visit TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_visit TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    total_page_views INT DEFAULT 1,
    INDEX idx_ip_address (ip_address),
    UNIQUE KEY unique_visitor (ip_address, user_agent_hash)
);

-- Simple stored procedure to update page stats
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS UpdatePageStats(IN p_page_slug VARCHAR(255))
BEGIN
    DECLARE total_count INT DEFAULT 0;
    DECLARE unique_count INT DEFAULT 0;
    DECLARE today_count INT DEFAULT 0;
    DECLARE first_visit_time TIMESTAMP DEFAULT NULL;
    DECLARE last_visit_time TIMESTAMP DEFAULT NULL;
    
    -- Get total visits
    SELECT COUNT(*) INTO total_count
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = p_page_slug;
    
    -- Get unique visits
    SELECT COUNT(DISTINCT CONCAT(ip_address, user_agent_hash)) INTO unique_count
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = p_page_slug;
    
    -- Get today's visits
    SELECT COUNT(*) INTO today_count
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = p_page_slug AND visit_date = CURDATE();
    
    -- Get first and last visit times
    SELECT MIN(visit_timestamp), MAX(visit_timestamp) INTO first_visit_time, last_visit_time
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = p_page_slug;
    
    -- Update or insert page stats
    INSERT INTO aachipsc_blog_page_stats 
        (page_slug, total_visits, unique_visits, today_visits, first_visit, last_visit)
    VALUES 
        (p_page_slug, total_count, unique_count, today_count, first_visit_time, last_visit_time)
    ON DUPLICATE KEY UPDATE
        total_visits = total_count,
        unique_visits = unique_count,
        today_visits = today_count,
        first_visit = COALESCE(first_visit, first_visit_time),
        last_visit = last_visit_time;
END //
DELIMITER ;
