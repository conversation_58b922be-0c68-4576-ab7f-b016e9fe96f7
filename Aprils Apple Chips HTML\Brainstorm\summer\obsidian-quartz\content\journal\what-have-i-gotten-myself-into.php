<?php
// Auto-generated blog post
// Source: what-have-i-gotten-myself-into.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'What have I gotten myself into? - July 2017';
$meta_description = 'The apartment smelled like burnt coffee and resentment. <PERSON> was constantly in pain from her chronic illness. On the best of days she was kind, laid back, personable. On her worst days she was a bully. She would verbally berate me if I asserted a boundary or told her no.';
$meta_keywords = 'journal, homeless, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'What have I gotten myself into? - July 2017',
  'author' => 'A. A. Chips',
  'date' => '2017-07-10',
  'excerpt' => 'The apartment smelled like burnt coffee and resentment. Mandy was constantly in pain from her chronic illness. On the best of days she was kind, laid back, personable. On her worst days she was a bully. She would verbally berate me if I asserted a boundary or told her no.',
  'tags' => 
  array (
    0 => 'journal',
    1 => 'homeless',
  ),
  'categories' => 
  array (
    0 => 'Journal',
  ),
  'source_file' => 'content\\journal\\what-have-i-gotten-myself-into.md',
);

// Raw content
$post_content = '<p>The apartment smelled like burnt coffee and resentment. Mandy was constantly in pain from her chronic illness. On the best of days she was kind, laid back, personable. On her worst days she was a bully. She would verbally berate me if I asserted a boundary or told her no. On several occasions I had to walk out and just stay in my car again behind the gas station till she cooled down and we made up.</p>

<p>I did my best to not let her unstable moments chip away at me, thinking I was the stronger one for taking the higher road. But in the long run, it impacted my self-esteem and self-worth greatly. It gave me this inner voice, that I have unlearned since. But it was a ridiculous caricature of some of the things I left behind prior. The kitchen was always a space of power struggle, even though she desperately needed help keeping up with the dishes and cleaning the countertops.</p>

<p>It was the first time someone hosting me indoors lead to an intimate relationship. And at first, things were great. Instability and fun go hand in hand. She was spontaneous, always egging us to go on excursions to swim at the waterfall, or do a craft project, or simply to stay in and get really spicy together. But within a month, it had turned more and more into verbal attacks, shaming, and nastiness.</p>

<p>Every week she would have a visit with her three year old son, and it would be the best day of the week. We would have lots of fun, and she was present as a parent, and I got to be a supporting role in that beautiful bonding time. I\'d be there to ensure drop offs and pick ups happen on time, as chronic lateness was one of the big challenges to co-parenting.</p>

<p>She\'d love going to live shows, and I would go with her. I used to love music shows. After some of the things I went through while traveling, such as long range acoustic warfare and riot police, the shows and the noise and the crowds would trigger sensory meltdown in me. I\'d know it was coming. I\'d communicate that when it was, I just needed silence, and that talking sounded like razor blades against my ears. Silence wasn\'t a preference, it was a need.</p>

<p>But she would ignore my request for compromise, and amp up and yell at me to evoke a reaction. Then I would just leave. She would hurl the word \'retarded\' like a brick, similar to my mother and how she\'d use that word to describe her dog (after claiming to advocate for my learning disability in grade school for years.)</p>

<p>I\'d opt to stay in my car in July, when the heat turns the car into a sweatbox. One night I was parked in the corner of the lot. The only way to sleep was with the windows half open and being almost nude. There had been a street festival nearby. I woke up from a nightmare at 3:30 AM. It was like a premonition of being ambushed by angry people. I woke up from it calm and self-aware of my surroundings. I hear a group of people talking about my car parked in the lot, with the windows open. At first I thought about the ambush dream, but the voice sounded like Mandy\'s. Footsteps were coming closer to my car. I bolt up and give my rehearsed, "Good Morning!" That lady bolted from the car fast.</p>

<p>---</p>

<p>Winter would be worse. I needed to winterize the car, finish my training, get out by January. But first, I had to survive the summer—the heat, the Nazis crawling out of their holes, the dreams where shadows reached for my throat.</p>

<p>I wasn’t a victim. I was a prisoner of war, and war had its own logic:</p>

<p><strong>Run. Rest. Repeat.</strong></p>

<p>---</p>
<h3><strong>Why Share This?</strong></h3>

<p>Because someone out there is slurping soup too loud in a house that hates them.</p>
<p>Because _"You’re overreacting"_ is the anthem of abusers.</p>
<p>Because survival isn’t pretty—but it’s _yours_.</p>

<p>And you don’t owe anyone the lie that it’s easy.</p>


';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>