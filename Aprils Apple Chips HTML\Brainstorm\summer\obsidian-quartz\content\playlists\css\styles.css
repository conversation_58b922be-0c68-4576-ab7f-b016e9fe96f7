/* Base Styles */
:root {
    --primary-color: #6200ea;
    --primary-light: #9d46ff;
    --primary-dark: #0a00b6;
    --secondary-color: #03dac6;
    --background-color: #121212;
    --surface-color: #1e1e1e;
    --on-background: #ffffff;
    --on-surface: #e0e0e0;
    --on-primary: #ffffff;
    --error-color: #cf6679;
    --border-radius: 8px;
    --transition-speed: 0.3s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    color: var(--on-background);
    line-height: 1.6;
}

.app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header Styles */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.playlist-info h1 {
    font-size: 2rem;
    margin-bottom: 5px;
    color: var(--on-background);
}

.playlist-info p {
    color: var(--on-surface);
    font-size: 1rem;
}

.search-container {
    display: flex;
    align-items: center;
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-top: 10px;
}

#search-input {
    background-color: transparent;
    border: none;
    padding: 10px 15px;
    color: var(--on-surface);
    font-size: 1rem;
    width: 200px;
}

#search-input:focus {
    outline: none;
}

#search-button {
    background-color: var(--primary-color);
    border: none;
    color: var(--on-primary);
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color var(--transition-speed);
}

#search-button:hover {
    background-color: var(--primary-light);
}

/* Main Content Styles */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
}

@media (min-width: 992px) {
    .main-content {
        grid-template-columns: 2fr 1fr;
    }
    
    .now-playing-container {
        grid-column: 1;
    }
    
    .playlist-container {
        grid-column: 2;
        grid-row: 1 / 3;
    }
    
    .lyrics-container {
        grid-column: 1;
    }
}

/* Now Playing Section */
.now-playing-container {
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.now-playing-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
}

@media (min-width: 768px) {
    .now-playing-info {
        flex-direction: row;
        align-items: flex-start;
    }
}

.album-art-container {
    position: relative;
    width: 250px;
    height: 250px;
    margin-bottom: 20px;
    perspective: 1000px;
}

@media (min-width: 768px) {
    .album-art-container {
        margin-right: 30px;
        margin-bottom: 0;
    }
}

#album-art {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    transition: transform 1s;
}

.vinyl-record {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: 
        radial-gradient(circle at center, 
            #000 0%, #000 20%, 
            transparent 20%, transparent 30%, 
            #000 30%, #000 35%, 
            transparent 35%, transparent 40%, 
            #000 40%, #000 45%, 
            transparent 45%, transparent 50%, 
            #000 50%, #000 55%, 
            transparent 55%);
    z-index: -1;
    transform: translateZ(-10px);
    opacity: 0;
    transition: opacity 0.5s, transform 1s;
}

.playing .vinyl-record {
    opacity: 1;
    animation: spin 4s linear infinite;
}

.playing #album-art {
    transform: translateZ(20px);
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.track-info {
    flex: 1;
    padding: 10px;
}

.track-info h2 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    color: var(--on-background);
}

.track-info h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--secondary-color);
}

.track-info p {
    color: var(--on-surface);
    margin-bottom: 5px;
}

/* Player Controls */
.player-controls {
    width: 100%;
}

.progress-container {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

#current-time, #duration {
    font-size: 0.9rem;
    color: var(--on-surface);
    width: 45px;
    text-align: center;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    margin: 0 10px;
    cursor: pointer;
    position: relative;
}

.progress {
    height: 100%;
    background-color: var(--secondary-color);
    border-radius: 3px;
    width: 0;
    position: relative;
}

.progress::after {
    content: '';
    position: absolute;
    right: -6px;
    top: -3px;
    width: 12px;
    height: 12px;
    background-color: var(--on-background);
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.2s;
}

.progress-bar:hover .progress::after {
    transform: scale(1);
}

.control-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
}

.control-button {
    background: none;
    border: none;
    color: var(--on-surface);
    font-size: 1.2rem;
    margin: 0 10px;
    cursor: pointer;
    transition: color var(--transition-speed), transform var(--transition-speed);
}

.control-button:hover {
    color: var(--on-background);
    transform: scale(1.1);
}

.play-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--on-primary);
    font-size: 1.2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 15px;
    transition: background-color var(--transition-speed), transform var(--transition-speed);
}

.play-button:hover {
    background-color: var(--primary-light);
    transform: scale(1.1);
}

.volume-container {
    position: relative;
    display: flex;
    align-items: center;
}

.volume-slider-container {
    width: 0;
    overflow: hidden;
    transition: width var(--transition-speed);
    margin-left: 5px;
}

.volume-container:hover .volume-slider-container {
    width: 80px;
}

#volume-slider {
    width: 80px;
    -webkit-appearance: none;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    outline: none;
}

#volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    background: var(--secondary-color);
    border-radius: 50%;
    cursor: pointer;
}

/* Playlist Section */
.playlist-container {
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 600px;
    overflow-y: auto;
}

.playlist-container h2 {
    margin-bottom: 15px;
    color: var(--on-background);
    font-size: 1.5rem;
}

.playlist-tracks {
    list-style: none;
}

.playlist-track {
    padding: 10px;
    border-radius: var(--border-radius);
    margin-bottom: 8px;
    cursor: pointer;
    transition: background-color var(--transition-speed);
    display: flex;
    align-items: center;
}

.playlist-track:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.playlist-track.active {
    background-color: rgba(98, 0, 234, 0.2);
    border-left: 3px solid var(--primary-color);
}

.track-number {
    width: 30px;
    text-align: center;
    font-weight: bold;
    color: var(--on-surface);
}

.track-details {
    flex: 1;
}

.track-title {
    font-weight: bold;
    color: var(--on-background);
    margin-bottom: 3px;
}

.track-artist {
    font-size: 0.9rem;
    color: var(--on-surface);
}

.track-duration {
    color: var(--on-surface);
    font-size: 0.9rem;
}

/* Lyrics Section */
.lyrics-container {
    background-color: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: 30px;
}

.lyrics-container.hidden {
    display: none;
}

.lyrics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.lyrics-header h2 {
    color: var(--on-background);
    font-size: 1.5rem;
}

#close-lyrics-button {
    background: none;
    border: none;
    color: var(--on-surface);
    font-size: 1.2rem;
    cursor: pointer;
    transition: color var(--transition-speed);
}

#close-lyrics-button:hover {
    color: var(--on-background);
}

.lyrics-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
    color: var(--on-surface);
    line-height: 1.8;
}

/* Footer Styles */
.app-footer {
    text-align: center;
    padding: 20px 0;
    margin-top: 30px;
    color: var(--on-surface);
    font-size: 0.9rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Utility Classes */
.hidden {
    display: none;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-light);
}
