-- Visitor Counter Database Schema
-- For A. A. Chips' Obsidian-Quartz Blog
-- Adds visitor tracking tables to existing database structure

-- Page visits table - tracks individual page visits
CREATE TABLE IF NOT EXISTS aachipsc_blog_page_visits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_slug VARCHAR(255) NOT NULL,
    page_title VARCHAR(500) DEFAULT NULL,
    ip_address VARCHAR(45) NOT NULL, -- IPv6 compatible
    user_agent_hash VARCHAR(64) NOT NULL, -- SHA256 hash of user agent for privacy
    session_id VARCHAR(128) DEFAULT NULL, -- PHP session ID if available
    visit_date DATE NOT NULL,
    visit_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_unique_daily BOOLEAN DEFAULT TRUE, -- Whether this is the first visit from this IP today
    is_unique_total BOOLEAN DEFAULT TRUE, -- Whether this is the first visit from this IP ever
    referrer VARCHAR(500) DEFAULT NULL,
    INDEX idx_page_slug (page_slug),
    INDEX idx_ip_address (ip_address),
    INDEX idx_visit_date (visit_date),
    INDEX idx_visit_timestamp (visit_timestamp),
    INDEX idx_unique_daily (page_slug, visit_date, is_unique_daily),
    INDEX idx_unique_total (page_slug, is_unique_total),
    UNIQUE KEY unique_daily_visit (page_slug, ip_address, user_agent_hash, visit_date)
);

-- Page statistics table - aggregated counts for fast retrieval
CREATE TABLE IF NOT EXISTS aachipsc_blog_page_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_slug VARCHAR(255) NOT NULL UNIQUE,
    page_title VARCHAR(500) DEFAULT NULL,
    total_visits INT DEFAULT 0,
    unique_visits INT DEFAULT 0,
    today_visits INT DEFAULT 0,
    today_unique_visits INT DEFAULT 0,
    last_visit TIMESTAMP DEFAULT NULL,
    first_visit TIMESTAMP DEFAULT NULL,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_page_slug (page_slug),
    INDEX idx_total_visits (total_visits),
    INDEX idx_unique_visits (unique_visits),
    INDEX idx_last_visit (last_visit)
);

-- Site-wide statistics table - overall site visit counts
CREATE TABLE IF NOT EXISTS aachipsc_blog_site_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stat_name VARCHAR(100) NOT NULL UNIQUE,
    stat_value BIGINT DEFAULT 0,
    stat_date DATE DEFAULT NULL, -- For daily stats
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_stat_name (stat_name),
    INDEX idx_stat_date (stat_date)
);

-- Insert initial site-wide statistics
INSERT IGNORE INTO aachipsc_blog_site_stats (stat_name, stat_value) VALUES
('total_site_visits', 0),
('unique_site_visitors', 0),
('total_pages_visited', 0),
('site_launch_date', UNIX_TIMESTAMP(NOW()));

-- Visitor sessions table - tracks unique visitors across the site
CREATE TABLE IF NOT EXISTS aachipsc_blog_visitor_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    user_agent_hash VARCHAR(64) NOT NULL,
    session_id VARCHAR(128) DEFAULT NULL,
    first_visit TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_visit TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    total_page_views INT DEFAULT 1,
    pages_visited TEXT DEFAULT NULL, -- JSON array of visited pages
    INDEX idx_ip_address (ip_address),
    INDEX idx_user_agent_hash (user_agent_hash),
    INDEX idx_session_id (session_id),
    INDEX idx_first_visit (first_visit),
    INDEX idx_last_visit (last_visit),
    UNIQUE KEY unique_visitor (ip_address, user_agent_hash)
);

-- Daily statistics table - for tracking daily trends
CREATE TABLE IF NOT EXISTS aachipsc_blog_daily_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stat_date DATE NOT NULL,
    total_visits INT DEFAULT 0,
    unique_visitors INT DEFAULT 0,
    pages_visited INT DEFAULT 0,
    top_page VARCHAR(255) DEFAULT NULL,
    top_page_visits INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_stat_date (stat_date),
    UNIQUE KEY unique_date (stat_date)
);

-- Create a view for easy access to current page statistics
CREATE OR REPLACE VIEW aachipsc_blog_page_stats_view AS
SELECT 
    ps.page_slug,
    ps.page_title,
    ps.total_visits,
    ps.unique_visits,
    ps.today_visits,
    ps.today_unique_visits,
    ps.last_visit,
    ps.first_visit,
    COALESCE(today_stats.today_total, 0) as today_total_calculated,
    COALESCE(today_stats.today_unique, 0) as today_unique_calculated
FROM aachipsc_blog_page_stats ps
LEFT JOIN (
    SELECT 
        page_slug,
        COUNT(*) as today_total,
        COUNT(DISTINCT CONCAT(ip_address, user_agent_hash)) as today_unique
    FROM aachipsc_blog_page_visits 
    WHERE visit_date = CURDATE()
    GROUP BY page_slug
) today_stats ON ps.page_slug = today_stats.page_slug;

-- Stored procedure to update page statistics (for performance)
DELIMITER //
CREATE OR REPLACE PROCEDURE UpdatePageStats(IN p_page_slug VARCHAR(255))
BEGIN
    DECLARE total_count INT DEFAULT 0;
    DECLARE unique_count INT DEFAULT 0;
    DECLARE today_count INT DEFAULT 0;
    DECLARE today_unique_count INT DEFAULT 0;
    DECLARE first_visit_time TIMESTAMP DEFAULT NULL;
    DECLARE last_visit_time TIMESTAMP DEFAULT NULL;
    
    -- Get total visits
    SELECT COUNT(*) INTO total_count
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = p_page_slug;
    
    -- Get unique visits
    SELECT COUNT(DISTINCT CONCAT(ip_address, user_agent_hash)) INTO unique_count
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = p_page_slug;
    
    -- Get today's visits
    SELECT COUNT(*) INTO today_count
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = p_page_slug AND visit_date = CURDATE();
    
    -- Get today's unique visits
    SELECT COUNT(DISTINCT CONCAT(ip_address, user_agent_hash)) INTO today_unique_count
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = p_page_slug AND visit_date = CURDATE();
    
    -- Get first and last visit times
    SELECT MIN(visit_timestamp), MAX(visit_timestamp) INTO first_visit_time, last_visit_time
    FROM aachipsc_blog_page_visits 
    WHERE page_slug = p_page_slug;
    
    -- Update or insert page stats
    INSERT INTO aachipsc_blog_page_stats 
        (page_slug, total_visits, unique_visits, today_visits, today_unique_visits, first_visit, last_visit)
    VALUES 
        (p_page_slug, total_count, unique_count, today_count, today_unique_count, first_visit_time, last_visit_time)
    ON DUPLICATE KEY UPDATE
        total_visits = total_count,
        unique_visits = unique_count,
        today_visits = today_count,
        today_unique_visits = today_unique_count,
        first_visit = COALESCE(first_visit, first_visit_time),
        last_visit = last_visit_time;
END //
DELIMITER ;

-- Stored procedure to clean old visit data (optional maintenance)
DELIMITER //
CREATE OR REPLACE PROCEDURE CleanOldVisitData(IN days_to_keep INT)
BEGIN
    DELETE FROM aachipsc_blog_page_visits 
    WHERE visit_timestamp < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    DELETE FROM aachipsc_blog_daily_stats 
    WHERE stat_date < DATE_SUB(CURDATE(), INTERVAL days_to_keep DAY);
END //
DELIMITER ;
