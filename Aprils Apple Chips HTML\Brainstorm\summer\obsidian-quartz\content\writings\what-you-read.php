<?php
// Auto-generated blog post
// Source: what-you-read.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'News in the Digital Age - Why What You Read Matters';
$meta_description = 'An exploration of the relationship between economics and journalism, and how it affects the news we consume.';
$meta_keywords = 'journalism, economics, news, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://www.usatoday.com/gcdn/authoring/authoring-images/2023/11/14/USAT/71584950007-q-anon-shaman.jpg?width=1320&height=934&fit=crop&format=pjpg&auto=webp';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'News in the Digital Age - Why What You Read Matters',
  'author' => 'A. A. Chips',
  'date' => '2025-05-20',
  'tags' => 
  array (
    0 => 'journalism',
    1 => 'economics',
    2 => 'news',
  ),
  'excerpt' => 'An exploration of the relationship between economics and journalism, and how it affects the news we consume.',
  'categories' => 
  array (
    0 => 'Writings',
  ),
  'thumbnail' => 'https://www.usatoday.com/gcdn/authoring/authoring-images/2023/11/14/USAT/71584950007-q-anon-shaman.jpg?width=1320&height=934&fit=crop&format=pjpg&auto=webp',
  'source_file' => 'content\\writings\\what-you-read.md',
);

// Raw content
$post_content = '<p>Remember when news was just on TV or in a newspaper? Sounds ancient, right? Back then, it was pretty simple: you paid for a paper, or watched a broadcast, and ads helped cover the costs. Sometimes, big groups even funded certain news channels. The idea was clear: sell ads, get subscriptions, and maybe some grants. But even then, how much things cost could decide who got to know what.The Internet Changed EVERYTHING</p>

<p>Then came the internet, and suddenly, getting news got wild!</p>

<ul><li>Good stuff costs money: The really well-researched stories often get stuck behind "paywalls" (where you have to pay to read).</li>

<p><li>Free stuff can be iffy: The less reliable news is often totally free and super easy to find.</li></p>

<p><li>The "free" dilemma: It’s like when everyone shared music illegally in the early 2000s – trying to get around paywalls raises questions about what\'s right.</li></p>

<p><li>So much noise: The internet became a giant free-for-all of ideas, but it also got flooded with unverified info.</li></p>


<p>As one study put it, "Newspaper paywalls are slowly growing, but online news is still mostly free." Follow the Money, Find the Bias.</p>

<p>Who pays for the news can totally change what stories get told and how. Think about PBS, for example. They get a lot of money from huge companies like Bank of America and the Ford Foundation. So, you have to wonder: how tough will they be when covering a banking scandal or issues with the car industry? It\'s like asking your friend to tell a story about you, but you\'re paying them. They might leave out the embarrassing parts! Fast vs. Right: A Dangerous Game</p>

<p>News outlets are under a lot of pressure to be first with a story. This "publish first" mindset can cause big problems:</p>

<p><li>Breaking news isn\'t always accurate: The first reports often have info that hasn\'t been double-checked.</li></p>

<p><li>Corrections? Crickets: When they fix mistakes, hardly anyone notices compared to the original, wrong story.</li></p>

<p><li>The classic saying: "A lie will make it around the world twice before the truth has a chance to put its pants on." It\'s so true!</li></p>


<p>I saw this happen firsthand during the October 7th events. Some in my family believed everything they heard even when I shared facts that contradicted what they\'d heard. But months later, investigations showed a totally different picture, and a lot of those initial "facts" turned out to be wrong. When Making Money Breaks Journalism..</p>

<p>The chase for clicks and views can lead to some really bad ethical fails:</p>

<p>1. CNN\'s Russia Reporting Scandal (2017): Three journalists quit after a story had to be pulled because it didn\'t meet their own standards. Oops! (<a href="https://www.cbsnews.com/news/cnn-journalists-resign-story-retraction-anthony-scaramucci/" class="external-link">CBS News</a>)</p>

<p>2. Gawker\'s downfall (2012-2016): This website cared more about getting clicks than about what was right. They wrote some controversial (but probably true) stuff about a very rich tech billionaire, and it ended up costing them everything in lawsuits. (<a href="https://www.theatlantic.com/business/archive/2018/02/hogan-thiel-gawker-trial/554132/" class="external-link">The Atlantic</a>)</p>


<h3>So, What Can We Do?</h3>

<p>As news becomes even more digital and anyone can publish anything, we all have a role to play:</p>

<p><li>Support good news: Look for news sources that are honest and clear about their reporting.</li></p>

<p><li>Think about who\'s paying: Always consider who is funding the news you\'re reading.</li></p>

<p><li>Ethics over profit: Remember that sometimes, doing the right thing in journalism might not be the most profitable.</li></p>

<p><li>Be smart about sharing: Learn how to share information responsibly online.</li></p>


<p>In a world where making money can constantly mess with good journalism, we, as news consumers, need to be smarter and more critical than ever. It\'s up to us to seek out the truth!</p>


<h2>The Path Forward</h2>

<p>As we move toward an increasingly decentralized information ecosystem where anyone can publish, we all bear responsibility:</p>

<p><li>Support accountable, transparent journalism</li></p>
<p><li>Be conscious of funding sources when evaluating news</li></p>
<p><li>Recognize that true journalistic ethics may sometimes conflict with profit motives</li></p>
<p><li>Learn and apply ethical information-sharing practices in our own communications</li></ul></p>

<p>In a world where economic pressures constantly threaten journalistic integrity, informed media consumers must become more discerning than ever before.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>