<?php
// Auto-generated blog post
// Source: dans-story.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'I\'m 61 Years Old, and It Still Affects Me Today’  <PERSON>’s Alienation Horror Story';
$meta_description = '<PERSON> <PERSON>, a remarkable survivor of parental alienation, having experienced this psychological abuse both as a child and a parent. In this interview, <PERSON> shares the emotional turmoil he faced growing up, from being alienated from his father from the tender age of just nine months to watching his little sister undergo the same alienation tactics that he did as a child.';
$meta_keywords = 'alienation, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://i.ytimg.com/vi/IKJzMZOzL8M/maxresdefault.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'I\'m 61 Years Old, and It Still Affects Me Today’  Dan’s Alienation Horror Story',
  'date' => '2024-08-01',
  'excerpt' => 'Meet Dan, a remarkable survivor of parental alienation, having experienced this psychological abuse both as a child and a parent. In this interview, Dan shares the emotional turmoil he faced growing up, from being alienated from his father from the tender age of just nine months to watching his little sister undergo the same alienation tactics that he did as a child.',
  'categories' => 
  array (
    0 => 'Alienation',
  ),
  'tags' => 
  array (
    0 => 'alienation',
  ),
  'source' => 'https://www.youtube.com/watch?v=IKJzMZOzL8M',
  'author' => 'The Anti-Alienation Project',
  'thumbnail' => 'https://i.ytimg.com/vi/IKJzMZOzL8M/maxresdefault.jpg',
  'source_file' => 'content\\alienation\\dans-story.md',
);

// Raw content
$post_content = '<iframe width="560" height="315" src="https://www.youtube.com/embed/IKJzMZOzL8M?si=fhU21UdCMIydh0s7" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

<p>https://www.youtube.com/shorts/DJedUJa-paw</p>

<p><a href="https://www.youtube.com/@TheAnti-AlienationProject" class="external-link">The Anti-Alienation Project</a></p>

<p>14.9K subscribers</p>
<p>4,333 views Aug 1, 2024 [SALT LAKE CITY]</p>

<p>Meet Dan, a remarkable survivor of parental alienation, having experienced this psychological abuse both as a child and a parent. In this interview, Dan shares the emotional turmoil he faced growing up, from being alienated from his father from the tender age of just nine months to watching his little sister undergo the same alienation tactics that he did as a child. He talks about how watching his sister go through it eventually woke him up to the truth. You’ll hear how he tried to shield her from enduring the same fate, how he went on to become alienated from his own children, and why speaking out is so important to him today. This interview is a must-watch. Dan\'s courage in sharing his story is commendable, and it\'s conversations like these that pave the way for greater awareness and understanding of the deep-rooted impact of parental alienation.</p>

<p>Welcome to The Anti-Alienation Project, where we discuss all things parental alienation from the perspective of an adult child who\'s been through it. We aim to provide support and raise awareness about the effects of this issue.</p>

<p>Check out Dan\'s social media for more insights and updates. Stay connected with The Anti-Alienation Project through our website, newsletter, and social media platforms to learn more and support our initiatives.</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>