<?php
// Auto-generated category index
// Category: kitchen

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Apple Chip Kitchen';
$meta_description = 'Welcome to the Apple Chip Kitchen - where food meets philosophy, recipes meet sustainability, and every meal is an opportunity for mindful living.';
$meta_keywords = 'kitchen, posts, A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [];

// Category posts data
$category_posts = array (
  0 => 
  array (
    'title' => 'Favorite Chicken Slaw Recipe',
    'author' => 'A. A. Chips',
    'date' => '2022-01-01',
    'excerpt' => 'This is one of my favorite home recipes and comfort foods. Why? I can eat it for days on end. It\'s cheap to make. It\'s healthy. And it doesn\'t take a ...',
    'url' => 'Favorite Slaw Recipe.php',
    'tags' => 
    array (
      0 => 'applechipkitchen',
      1 => 'cook',
      2 => 'aachips',
      3 => 'videoscript',
    ),
    'filename' => 'Favorite Slaw Recipe',
    'thumbnail' => NULL,
  ),
);

// Generate content
ob_start();
?>
<div class="category-index">
    <header class="category-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
    </header>

    <div class="category-content">
        <?php echo <<<'HTML'
<h2>Welcome to the Apple Chip Kitchen</h2>

<p>Where food meets philosophy, recipes meet sustainability, and every meal is an opportunity for mindful living.</p>

<p>The kitchen is more than just a place to prepare food - it's a laboratory for creativity, a sanctuary for nourishment, and a bridge between cultures. Here you'll find recipes, food philosophy, and thoughts on how what we eat connects us to the world around us.</p>

<h3>What You'll Find Here</h3>

<ul><li><strong>Recipes</strong>: From simple comfort foods to cultural explorations</li>
<p><li><strong>Food Philosophy</strong>: Thoughts on ethics, sustainability, and mindful eating</li></p>
<p><li><strong>Cultural Connections</strong>: How food brings us together across boundaries</li></p>
<p><li><strong>Practical Tips</strong>: Making good food accessible and affordable</li></ul></p>

<p>Whether you're looking for a new recipe to try, exploring the ethics of food choices, or just curious about the stories behind our meals, welcome to the kitchen. Pull up a chair, grab a snack, and let's explore the wonderful world of food together.</p>

HTML;
        ?>
    </div>

    <div class="post-grid">
        <?php foreach ($category_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post['excerpt']) ? $post['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author'] && is_string($post['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>