---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQBGOIAGGjoghH0EDihmbgBtcDBQMBLoLChuCEBeDcBZnby8wU1SQgRYAF021JLIADNOKABlQiNxXgAObQB2AE5Zufm5gBZ+

Ur7sgDFcfUJ8GG4AVhXIKHKAQSJlLglgnoqjhiYocwJzwkvKqDY4LtKSSoBFABsPAAmjxiAAZAASP2O8FGZUwd0KkEw3Gci0WAGZ7ns0Dx9vEpvtJmNEocURAKCR1NwePEktosWN9jwxpieMy2Yl7pIEG9pNwsfttAsxXN7tZlMFuEl7swoKQ2ABrBAAYTY+DYpEqAGJ4ggDQbYRBNLhsMrlEqhBxiBqtTqJIrrMw4LhAtkTT0dvgBrAZRJJOaNI

ETQqlaqAOo0yTceLyxUqhB+mAB9CCDwm63KSQccK5NDxylfc1qPGoBlyynW2155gF1AcIT4fDyhAIYgHYVYovdB4sdhXVCY+6MAecABynDEcfi7P2+zGgMm9yEcGIuBOncLk0WYzZWMmi6SPGWlMIzAAIukt9xFUIEPcrcI4ABJYgNvJte6aYS2gCiwSZNkn7fpSRAcMqlT1I0zStDAHQmlqFodtwPQEGE9x6NkuCEHmpDvtwTYtvc2pvHhBAACr

lJUtQwXYcHtJ09zkBQ1FItBDQMS0TFIXhCBQvyOYVIW2gEpKQifAASuEwyjPej7nraWCVLgKT3D05CZIRaDEfghQAL4rMUpQnBxEh0XkACyMCoDJuCOBwyioAAUsIpB5ghzGUmsgxyXSVZ9r5mzbLsBz3GZUCvO81wILcJpjk87jRUO0BfCafwSBCgL4GMpwTsqpwmrAiAfDR9xomgzishMYxJJMwqTKSdUstMuJxosgIJFiy5zke9zUsQtL4ouC

SAvVPVJGMWLTOyZ59nyAoibwFJ9lKaaBaU4ZJva2p6kahpID+5qWjWdqantTrkBwrrulkyJBT6KZplIwYiEdlLbVGMZxgmEbJv6CIZn8LHCDmdYNr2pQltgZZxkkm2QGdENEc2rafe224rdMh6jkwrCcOFlJjgTHBThwM6Fsy8SAuys1Q5Aa4breO57geR51ae9wXtewQs6gClPtab4fvkYF9r+NrEIBGT3aB9wQVBEj1DZdkhI5zluSInmIQrbA

oVj6H4JhlLYVE/EEVjemkc0/hUTRFl1NZtn2RrrnuTr3l9qx7HLRAKsu+reGax7CBeXxeaCUtcZiatpS4JJbAyawIx3qQD7c8plXoLg8QQIZxmUpFtFOwA8hwuwC+QygdrZF4C0GUCoK+qB4BwADkTd6Cq2jaLrPn9EMqf4ojEDBVsOzlnHxxnBcqU3A9pSJc8+ApR86Xc1jEDamq/w8IQABC0zFfCZUcRV6J1V10xTdMgL7DfWJJG1lLls4T8TI

CwqzPEiycos8QGTzVKINYaw4khYm0ONJIgJOrTEmDwe+wJp5SCEoKEakonIbT+jtC6jp0D6kOsaY6FpnxS12vg6A11boekXr0J6gNKhBmwCGD6fYvoIGjENWMhYcGqmekDTUINqxg1zPmX6xY3Sw1gPDUeyNxG6TRm2VCaAsRqMmKPEmg4hR43HGTacoxpg0xgfEIxKCmabhURWXc+5pocxPMAyAPMbxWMFpSMhIt5aUklgBICcsxYKzwkrdA9Ry

6V2dDXYgddmAN03M3Vu1hO6tzYD3PuXtSjIVVIbDCik+xm1wvhHSjYlHFlthRfAvsS6hIrrZCJtdUD13UHElubcknd2VL3fu3tKCVOVnkMJtTq71MaY3eJrSu4pI6WkiOAk0HLSJOJSkCdpKyWHlXDOSliAqQkLgHg+cShGUKCZGe5l0CWQGVXaIwyYlNKbi0xJEzUldNWIPfyI8NL9BCpPImfZIpr1ivFXRSUXhz3Xt8TelQD770IFJZUABVOFJ

9SpOnKpSbOzhH7aAfnAqa8RJrTD4K/bgkxtCLEJNMBcM1piYhPNSgaP00CLAgVAhGsDATwMQfsZBvI5l0hQetUYo8OEUP2kQthpQzSkLOiKq6Lo3S0K9Aw1MCJmGsLDImb63CJHsI1QDZVlRgadlBn4MR9ZtXQykXDQsCNjW1gUcUkiGMrHCkWEY3RpMdHE3xoOcmlNUDMjGDfHgswVyUgsfzeINj2bHi5ueK8LisZuL7B498XiJZ/mln4kCATwJ

BM4hcupUSGk3NGfcjujypnPMgJkqxRsTZ5P6AUpgRTralPIhwe2pzqhlxqZcyJ0TYl3ISeW5JTz0lIx6Q7EJ/Te2FoHbcsZDzR2VvHRACCszo6iUWWtROyc3nrNyb8LOqksT7LAAZcA4sIC4DgHAP0ljuAmWgHyTIlQLiChWAwFoFAD4nTIbaGVBCejAZAz8CA2ARC0NfCcfQfp/qAYgIQg6YGIOkCgzB39UqM0IedDdeV90UOQfutBjI6wlUvUN

YRtDxGYNwaTFwsBhLSiofQxkOj/DGESEo5+ljNGMhSVESjXhhRwNEeyCR/QpdLUyOtZtUT1HxMwfWJ8ieYU0Bx3k6x/QynshD1GDwOTvHFMZF9v89AC8qNafvaQKKaG2AUD5A5VGjrmNiagBJ/8tpTh2Yc+rVSPnLN8f0N5pUbFT4SDOmB5g2AlQtgABpxgal1EkON4ENX2AjLln7ouxfwCCeGPUsV3x/jYmYJJP1GBSfoR9xMCAPllFiw8+59gF

xE0Z9zMGBNSyE+gSLn6rQkD0wFfrzRiB+gQN8dTI2SBWTYFszzuBNDBGycbQ9EABvnQdDVvsB9NRb1IMoM0AAKekK5eCRuoOds7jJ9gAEoTQyWUM2d0lQDvHc5HKXgT9Lsfcuzd+7rXXMKcGLqqTTxOANlbasLSCAZLKWaE5bbpQsiLeW2nDZeSiCTYPfcDtr60BJuhpJdd6OECA8gHYAAVggbAOQBgdrgLN+bHalu1pyZ+80TxGCURSfgJHcJkX

pnSLT7RWEhAKgMJRcLDr0Z9hrSt+t0ODADGF+DocdbD3VtCFFEX3PefOf0gc8Ahz6HBAbMAC9BkgA===
```
%%