# Apache configuration for obsidian-quartz
# Redirect root access to content/index.php

# Enable URL rewriting
RewriteEngine On

# Redirect root directory access to content/index.php
# This handles both obsidian-quartz/ and obsidian-quartz/index.html
RewriteRule ^/?$ content/index.php [R=302,L]
RewriteRule ^index\.html?$ content/index.php [R=302,L]

# Optional: Handle common variations
RewriteRule ^home/?$ content/index.php [R=302,L]

# Prevent content/content loops by redirecting any content/content/* paths
RewriteCond %{REQUEST_URI} ^/content/content/
RewriteRule ^content/content/(.*)$ content/$1 [R=301,L]

# Ensure proper MIME types for common files
<IfModule mod_mime.c>
    AddType text/css .css
    AddType application/javascript .js
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
</IfModule>

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
</IfModule>

