<?php
/**
 * Simple Authentication Handler
 * For A. A. Chips' Obsidian-Quartz Comments System
 * Replaces Google OAuth with simple name/email authentication
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/database.php';

class SimpleAuth {
    private $db;

    public function __construct() {
        $this->db = CommentDatabase::getInstance();
    }

    /**
     * Create or get user by name and email
     */
    public function createOrGetUser($name, $email) {
        // Validate input
        $name = trim($name);
        $email = trim(strtolower($email));
        
        if (empty($name) || empty($email)) {
            throw new Exception('Name and email are required');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email address');
        }
        
        if (strlen($name) < 2 || strlen($name) > 100) {
            throw new Exception('Name must be between 2 and 100 characters');
        }
        
        // Check if user exists
        $user = $this->getUserByNameAndEmail($name, $email);
        
        if (!$user) {
            // Create new user
            $user = $this->createUser($name, $email);
        }
        
        // Set session
        $this->setUserSession($user);
        
        return $user;
    }
    
    /**
     * Get user by name and email combination
     */
    private function getUserByNameAndEmail($name, $email) {
        $usersTable = $this->db->getTableNamePublic('users');
        $sql = "SELECT * FROM {$usersTable} WHERE name = :name AND email = :email AND is_banned = FALSE";
        $stmt = $this->db->getPDO()->prepare($sql);
        $stmt->execute([
            'name' => $name,
            'email' => $email
        ]);
        return $stmt->fetch();
    }

    /**
     * Create new user
     */
    private function createUser($name, $email) {
        $usersTable = $this->db->getTableNamePublic('users');
        $sql = "INSERT INTO {$usersTable} (name, email) VALUES (:name, :email)";
        $stmt = $this->db->getPDO()->prepare($sql);
        $stmt->execute([
            'name' => $name,
            'email' => $email
        ]);

        return $this->getUserByNameAndEmail($name, $email);
    }
    
    /**
     * Set user session
     */
    private function setUserSession($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['auth_time'] = time();
    }
    
    /**
     * Check if user is logged in (session-based)
     */
    public static function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    /**
     * Get current user from session
     */
    public static function getCurrentUser() {
        if (!self::isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'name' => $_SESSION['user_name'],
            'email' => $_SESSION['user_email'],
            'auth_time' => $_SESSION['auth_time'] ?? time()
        ];
    }
    
    /**
     * Clear user session
     */
    public static function logout() {
        unset($_SESSION['user_id']);
        unset($_SESSION['user_name']);
        unset($_SESSION['user_email']);
        unset($_SESSION['auth_time']);
    }
    
    /**
     * Validate email format
     */
    public static function isValidEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate name format
     */
    public static function isValidName($name) {
        $name = trim($name);
        return !empty($name) && strlen($name) >= 2 && strlen($name) <= 100;
    }
    
    /**
     * Get user by ID (for database operations)
     */
    public function getUserById($userId) {
        $usersTable = $this->db->getTableNamePublic('users');
        $sql = "SELECT * FROM {$usersTable} WHERE id = :id AND is_banned = FALSE";
        $stmt = $this->db->getPDO()->prepare($sql);
        $stmt->execute(['id' => $userId]);
        return $stmt->fetch();
    }
}

// Handle logout request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action']) && $_GET['action'] === 'logout') {
    SimpleAuth::logout();
    header('Content-Type: application/json');
    echo json_encode(['success' => true]);
    exit;
}
