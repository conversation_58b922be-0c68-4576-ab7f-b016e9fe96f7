<?php
// Auto-generated blog post
// Source: jul4-asthma-attack.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'July 4th Asthma Attack - Journal Entry';
$meta_description = 'Today I had a really bad asthma attack while at the apartment with her. It struck me when she once asked if I was dying or having an asthma attack. When I\'m in distress, I need calm.';
$meta_keywords = 'journal, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'July 4th Asthma Attack - Journal Entry',
  'author' => 'A. A. Chips',
  'excerpt' => 'Today I had a really bad asthma attack while at the apartment with her. It struck me when she once asked if I was dying or having an asthma attack. When I\'m in distress, I need calm.',
  'date' => '2017-07-04',
  'categories' => 
  array (
    0 => 'Journal',
  ),
  'tags' => 
  array (
    0 => 'journal',
  ),
  'source_file' => 'content\\journal\\jul4-asthma-attack.md',
);

// Raw content
$post_content = '<p>Today I had a really bad asthma attack while at the apartment with her. It struck me when she once asked if I was dying or having an asthma attack. What I really needed was a comforting "Do you need anything from me right now?" I feel incredibly alone despite knowing I\'ll be okay. I even asked her to turn the music up because I had no voice, and she got frustrated. I lacked the energy to deal with it. Thankfully, she stopped yelling when I asked her to. That\'s some progress, I suppose.</p>

<p>When I\'m in distress, I need calm. Passive-aggressive silence isn\'t calming. Calm requires active engagement. Just sitting with me and seeing if that helps, or asking if I need to be alone, would be appreciated. Guessing what I need is not the same as simply asking.</p>

<p>Monday I worked a full day, and the following Tuesday my recovery was awful. I truly don\'t understand how people manage 40+ hour work weeks. A week\'s pay wouldn\'t even cover a brief hospital visit.</p>

<p>I need a different kind of love. I\'m too quick to hope someone will rescue me from myself. I have a strong feeling that staying with this person will ultimately lead to serious health issues for me.</p>

<p>This all happened on the Fourth of July, a day when I couldn\'t breathe. When everything feels precious, then nothing truly does. Urgency often stems from someone else\'s priorities. I saw a telling phrase in the Family Dollar bathroom: "They have no god but money." If I could manifest my intentions into reality, what would they be? Our present actions have lasting consequences. Any story can be viewed from a different perspective. Is anyone out there listening?</p>

<p>We need a sense of belonging. Like attracts like. There should be a balance of give and take. When we mix observation with judgment, people tend to perceive it as criticism.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>