<?php
// Auto-generated blog post
// Source: sobriety.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'For two years I have chosen sobriety. Here\'s what that is about.';
$meta_description = array (
);
$meta_keywords = 'writings, personal, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/addiction.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'For two years I have chosen sobriety. Here\'s what that is about.',
  'author' => 'A. A. Chips',
  'date' => '2025-05-01',
  'categories' => 
  array (
    0 => 'Personal Reflections',
    1 => 'Writings',
  ),
  'tags' => 
  array (
    0 => 'writings',
    1 => 'personal',
  ),
  'excerpt' => 
  array (
  ),
  'thumbnail' => '../../img/addiction.jpg',
  'source_file' => 'content\\sobriety.md',
);

// Raw content
$post_content = '<p>For the past two years, I have chosen sobriety, a decision rooted in a desire for clarity, survival, and overall well-being. Embracing my pain fully has proven to be a profound gift, enabling me to confront my issues head-on and take meaningful action.</p>

<p>Most of my adult life involved daily cannabis use. While I appreciate a well-crafted drink, I\'m not a fan of alcohol, viewing it as a toxin that can make people insufferable. I have never used hard drugs, yet my work often places me in environments with individuals who are intoxicated or under the influence of harder substances.</p>

<p>I believe that all people, regardless of their struggles with drugs and alcohol, are beloved children of G-d. However, I acknowledge that their behavior can, at times, be annoying—a truth I can attest to from my own experiences while high.</p>

<p>Addiction is a complex condition, not a crime. While it can unfortunately lead individuals to commit offenses and engage in regrettable actions, I hold a deep well of compassion for those grappling with substance use. If someone\'s actions while under the influence cause harm, I believe in holding them accountable. Nevertheless, I do not equate substance use with criminality, though I may find certain behaviors associated with it frustrating. Regardless, these individuals deserve decency, connection, and the ability to meet their fundamental survival needs.</p>

<p>Extensive scholarly research supports the idea that the inverse of addiction is connection, and that isolation significantly contributes to its perpetuation. For instance, Dr. Gabor Maté, a renowned physician and author, extensively explores the profound link between early trauma, disconnection, and addiction in his work, such as In the Realm of Hungry Ghosts: Close Encounters with Addiction (2008). He argues that addiction is often a desperate attempt to cope with emotional pain and lack of genuine connection. Similarly, the "Rat Park" experiments conducted by Bruce Alexander in the late 1970s and early 1980s provided compelling evidence that rats in enriched environments with social interaction were less likely to self-administer drugs compared to those in isolated cages (Alexander et al., 1981). These studies underscored the critical role of environment and social connection in vulnerability to addiction.</p>

<p>From the lens of addiction, we can gain invaluable insights into societal failures and how we can better support our neighbors. It is demonstrably more cost-effective and efficacious to approach these issues with kindness and compassion rather than punitive measures. Yet, for decades, society has predominantly opted for punishment at every turn. Research by organizations like the National Institute on Drug Abuse (NIDA) consistently highlights that treatment for substance use disorders is significantly less expensive than incarceration and yields better long-term outcomes (NIDA, 2019). Implementing harm reduction strategies and expanding access to evidence-based treatment programs not only improve individual lives but also reduce the broader societal burdens associated with addiction.</p>

<p>I aspire to the clarity, focus, and richness of life that sobriety offers—embracing both its pain and its joy. Cannabis served its purpose during a particular phase of my life, helping me navigate a challenging period. I no longer need it; my life is unequivocally better without it. I am frequently around people who use substances, and I firmly believe they are individuals deserving of decency.</p>

<img src="../../img/addiction.jpg" alt="Having an addiction doesn\'t make you a bad person." width="400">

<p>References:</p>

<ul><li>Alexander, B. K., Coambs, R. B., & Hadaway, B. F. (1981). The effect of housing and environment on morphine self-administration in rats. Psychopharmacology, 73(4), 384-388.</li>
<p><li>Maté, G. (2008). In the Realm of Hungry Ghosts: Close Encounters with Addiction. North Atlantic Books.</li></p>
<p><li>National Institute on Drug Abuse (NIDA). (2019). Principles of Drug Addiction Treatment: A Research-Based Guide (Third Edition). Retrieved from https://www.drugabuse.gov/publications/principles-drug-addiction-treatment-research-based-guide-third-edition</li></ul></p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>