<?php
// Auto-generated blog post
// Source: 404-page.md

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = '404 Page Not Found';
$meta_description = 'Hi welcome to <PERSON>\'s can we take your order?';
$meta_keywords = '';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [
    ['title' => 'Alienation', 'url' => '../content/alienation/index.php', 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => '../content/climate/index.php', 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => '../content/humor/index.php', 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => '../content/inspiration/index.php', 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => '../content/journal/index.php', 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => '../content/judaism/index.php', 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => '../content/kitchen/index.php', 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Street', 'url' => '../content/street/index.php', 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => '../content/writings/index.php', 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '404 Page Not Found',
  'author' => 'A. A. Chips',
  'excerpt' => 'Hi welcome to Wendy\'s can we take your order?',
  'source_file' => 'content\\404-page.md',
);

// Raw content
$post_content = '<p>Hi welcome to Wendy\'s can we take your order?</p>

<p>The page you are looking for could not be found.</p>

<p><a href="http://localhost/" class="external-link">Return to Home</a></p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>