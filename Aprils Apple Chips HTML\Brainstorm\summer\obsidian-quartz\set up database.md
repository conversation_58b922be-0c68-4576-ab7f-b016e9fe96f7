1. **Set up the database:**
    - Create a MySQL database
    - Import the `comments/comments.sql` schema
2. **Configure Google OAuth:**
    - Create a Google Cloud Console project
    - Set up OAuth 2.0 credentials
    - Update `comments/config.php` with your credentials
3. **Update configuration:**
    - Edit database credentials in `comments/config.php`
    - Add your Google ID to the admin list
    - Adjust spam detection and rate limiting settings as needed
4. **Test the system:**
    - Visit 
        
        `test-comments.php`
        
         to verify functionality
    - Test authentication, commenting, voting, and replies