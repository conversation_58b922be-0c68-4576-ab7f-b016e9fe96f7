<?php
// Auto-generated blog post
// Source: why-didnt-she-leave.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Why Didn\'t She Just Leave?';
$meta_description = '';
$meta_keywords = 'memes, dv, library, resources, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/swancpr.jpg';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Why Didn\'t She Just Leave?',
  'author' => 'Anonymous',
  'tags' => 
  array (
    0 => 'memes',
    1 => 'dv',
    2 => 'library',
    3 => 'resources',
  ),
  'thumbnail' => '../../img/swancpr.jpg',
  'source_file' => 'content\\inspiration\\why-didnt-she-leave.md',
);

// Raw content
$post_content = '<h1>Why Didn\'t She Just Leave?</h1>

<p>I found this reposted on a social media site and liked it.</p>

<p>Related: <a href="../street/when-choice-isnt-choice.php">If you choose to be homeless, it means your choices in the situation were terrible.</a></p>

<p>Anonymous:</p>

<p>“Why we should never say "But why don\'t you just leave?" to women in toxic, highly problematic marriages.</p>

<p>🌟 She was taught to fight for her marriage.</p>
<p>🌟 She can\'t support herself or her children.</p>
<img src="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png" alt="🌟"> She sees herself as a contributor to the problems; she\'s hoping he will improve as she improves.
<p>🌟 She\'s doesn\'t have anywhere to go.</p>
<p>🌟 She\'s too overwhelmed to think clearly about her next step.</p>
<p>🌟 She believes God hates divorce.</p>
<p>🌟 Her spouse has promised to complicate her life if she leaves.</p>
<p>🌟 She doesn\'t want to fail.</p>
<p>🌟 She\'s heard, "marriage is hard, divorce is hard, choose your hard." She\'s chosen her hard.</p>

<p>🌟 Her church doesn\'t allow divorce other than for infidelity (and even then, they would first have to try and reconcile.)</p>
<p>🌟 Her spiritual sounding board, her church, is silent on abuse and consequences of unrepentant harmful sin in marriage. She\'s in the dark.</p>
<p>🌟 Other people have gone through worse, and they stayed put.</p>
<p>🌟 She wants her children to have both parents.</p>
<p>🌟 She\'s heard testimonies where people stayed and prayed, and their spouses changed after a long time.</p>
<p>🌟 She\'s dependent on her husband for documentation as an immigrant.</p>
<p>🌟 He\'s never hit her.</p>
<p>🌟 Sometimes she thinks the marriage problems are not that bad.</p>
<p>🌟 He\'s a man of God, and she doesn\'t want to ruin his ministry.</p>
<p>🌟 She loves him.</p>

<p>👉🏾 You see, there are bazillion reasons why a wife won\'t "just leave." It\'s more complicated than we think.”</p>

<img src="../../img/swancpr.jpg" width="250" alt="banksy art girl cpr on dead swan.">




';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>