<?php
// Auto-generated blog post
// Source: car-partment.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Car-Partment - A Poem';
$meta_description = 'Through rough times and good, Bounty and hunger, embrace and alienation, I’ve always had wheels, always had a net, I’ve always had my car-partment…';
$meta_keywords = 'homeless, journal, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Car-Partment - A Poem',
  'author' => 'A. A. Chips',
  'categories' => 
  array (
    0 => 'Journal',
    1 => 'Inspiration',
  ),
  'tags' => 
  array (
    0 => 'homeless',
    1 => 'journal',
  ),
  'date' => '2018-06-06',
  'excerpt' => 'Through rough times and good, Bounty and hunger, embrace and alienation, I’ve always had wheels, always had a net, I’ve always had my car-partment…',
  'source_file' => 'content\\journal\\car-partment.md',
);

// Raw content
$post_content = '<p>#aachips #homeless #love-story #april #accessibility #stories</p>


<img src="../../img/car-bed.jpg" alt="Bed in back of my old car with sheet and stuffed animal and pillows." width="250">

<p>Through rough times and good,</p>
<p>Bounty and hunger, embrace and alienation,</p>
<p>I’ve always had wheels, always had a net,</p>
<p>I’ve always had my car-partment…</p>

<p>I love my car-partment, she cares for me well,</p>
<p>Always a nest, a roof for me to dwell,</p>
<p>A place to keep snacks on the go,</p>
<p>Change my clothes, and de-fur them like a pro,</p>

<p>A safe getaway when a situation is bad,</p>
<p>Sometimes being a tran gets some folks mad,</p>
<p>I know I have shelter, I know I have warmth,</p>
<p>With thanks to my car-partment…</p>

<p>When my car-partment breaks I get stressed and blue,</p>
<p>Arms have too much play, or steering gets loose</p>
<p>I knew I must learn mechanics, else I be on the streets,</p>
<p>Fight before admitting defeat.</p>

<p>Before the past couple years, I couldn’t change a wheel,</p>
<p>Or top my oil, the shops would take me for a fool,</p>
<p>I’ve learned some things, like rotating my wheels and service my brakes,</p>
<p>Keep fluids up and replace some bushings when she starts to shake</p>
<p>I’d like her to live forever, but that’s not my car-partment’s fate.</p>

<p>I’ve never been homeless, just caught without a house,</p>
<p>There are many worse off, but my life is to espouse,</p>
<p>Her steering is failing and my life falling apart,</p>
<p>For a time I’d like a place for my house to park,</p>

<img src="../img/carbookshelf1.jpg" alt="carbookshelf1.jpg">

<p>For the rest of my <a href="index.php">Journal Archives</a> vault go here.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>