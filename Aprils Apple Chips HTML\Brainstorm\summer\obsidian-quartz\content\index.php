<?php
// Auto-generated dynamic content index
// Shows posts from content root directory

// Load path helper and configuration
require_once __DIR__ . '/../path-helper.php';
$config = include __DIR__ . '/../config.php';
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Welcome to my Digital Garden - A. A. Chips';
$meta_description = 'Personal stories, advocacy work, and reflections';
$meta_keywords = 'A. A. Chips, blog, personal stories, advocacy';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$related_posts = [
    ['title' => 'Alienation', 'url' => 'content/alienation/index.php', 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => 'content/climate/index.php', 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => 'content/humor/index.php', 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => 'content/inspiration/index.php', 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => 'content/journal/index.php', 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => 'content/judaism/index.php', 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => 'content/kitchen/index.php', 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => 'content/ptsd-myth/index.php', 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => 'content/street/index.php', 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => 'content/writings/index.php', 'excerpt' => 'Browse all Writings posts'],
];

// Content root posts data
$content_posts = array (
  0 => 
  array (
    'title' => '100 Things I Know About Myself',
    'author' => 'A. A. Chips',
    'date' => NULL,
    'excerpt' => 'This was an exercise for our stand up comedy support group. I hope reading through it doesn\'t cause you to eat rocks.',
    'url' => '100-things-about-myself.php',
    'tags' => 
    array (
      0 => 'personal',
      1 => 'writings',
    ),
    'filename' => '100-things-about-myself',
    'thumbnail' => '../../img/lurkLaughLoathe.png',
  ),
  1 => 
  array (
    'title' => 'I am a founding patron at the 12 Baskets Café. Here is what that means to me.',
    'author' => 'A. A. Chips',
    'date' => '2025-06-06',
    'excerpt' => 'I am a founding patron at the 12 Baskets Café. Here is what that means to me.',
    'url' => 'founding-patron-12b.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'advocacy',
      2 => 'alienation',
      3 => 'accessibility',
      4 => 'a11y',
      5 => 'april',
      6 => 'CompassionateCities',
    ),
    'filename' => 'founding-patron-12b',
    'thumbnail' => '../../img/self/april-garden.jpg',
  ),
  2 => 
  array (
    'title' => 'A Day of Joy for Incarcerated Moms',
    'author' => 'A. A. Chips',
    'date' => '2025-05-31',
    'excerpt' => 'May 31st marked the annual Mother Teen Day Retreat at the Swannanoa Women\'s Correctional Facility, a deeply meaningful event orchestrated by a coalition of dedicated faith communities, including my own. This retreat isn\'t just another day; it\'s a beacon of hope anticipated throughout the year by the incarcerated mothers, representing their only annual opportunity to connect, face-to-face, with their children.',
    'url' => 'mom-teen-retreat.php',
    'tags' => 
    array (
      0 => 'advocacy',
      1 => 'teens',
      2 => 'journal',
      3 => 'personal',
    ),
    'filename' => 'mom-teen-retreat',
    'thumbnail' => '../../img/wccw-sign.jpg',
  ),
  3 => 
  array (
    'title' => 'Chip Off the Old Block',
    'author' => NULL,
    'date' => '2025-05-20',
    'excerpt' => 'Recently I was invited to participate in a local art project called Unlabel Me. This project addresses the stigmas and labels that are used to dehumanize us, and break through them. I am planning to speak for their showcase event on June 13 for 2-4 minutes. I was given a prompt with several open ended reflection questions.',
    'url' => 'chip-off-old-block.php',
    'tags' => 
    array (
      0 => 'personal',
      1 => 'identity',
      2 => 'family',
      3 => 'reflection',
      4 => 'draft',
    ),
    'filename' => 'chip-off-old-block',
    'thumbnail' => '../img/self/chipoffblock.png',
  ),
  4 => 
  array (
    'title' => 'Guide to leaving really bad testimonials about Apple Chips',
    'author' => 'A. A. Chips',
    'date' => NULL,
    'excerpt' => 'I hope you enjoyed your apple chips. I have a small request. I am asking people to leave testimonials on the website about their chip experience. But here’s the thing. When it’s positive testimonials. They are really boring. I am asking people I trust to leave really scathing bad testimonials blaming the apple chips for their problems.',
    'url' => 'bad-chip-testimonial-guide.php',
    'tags' => 
    array (
      0 => 'aachips',
    ),
    'filename' => 'bad-chip-testimonial-guide',
    'thumbnail' => 'https://appleforthat.stemilt.com/wp-content/uploads/2016/06/Apple-Chips.jpg',
  ),
  5 => 
  array (
    'title' => 'Table of Contents - A. A. Chips',
    'author' => NULL,
    'date' => '2025-05-22',
    'excerpt' => 'Here\'s a list with all the contents of the site. Good luck, I wish you the best.',
    'url' => 'contents.php',
    'tags' => 
    array (
    ),
    'filename' => 'contents',
    'thumbnail' => NULL,
  ),
  6 => 
  array (
    'title' => 'I like my space, but someone or someones need me to show up in the world more',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'I value my equilibrium and independence. I love being in my space in quiet, and don\'t crave leaving, or get the dreaded FOMO. I need time alone to process emotions. I\'ve developed strong methods for maintaining my spiritual and emotional balance. I\'ve retreated from past friendships to protect my energy, also due to past experiences.',
    'url' => 'come-to-me.php',
    'tags' => 
    array (
      0 => 'journal',
      1 => 'personal',
      2 => 'relationships',
    ),
    'filename' => 'come-to-me',
    'thumbnail' => NULL,
  ),
  7 => 
  array (
    'title' => 'Hadestown. Teen Edition. My Review.',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'Someone I know was performing in Hadestown. Teen Edition. I got gifted tickets. I went to see it. I am not a theater or musical person, but I listen to musical soundtracks and knew every other word. There was not a dry eye that night.',
    'url' => 'hadestown-review.php',
    'tags' => 
    array (
      0 => 'journal',
    ),
    'filename' => 'hadestown-review',
    'thumbnail' => '../../img/hadestown.jpg',
  ),
  8 => 
  array (
    'title' => 'Home Tour',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'I originally put this together as part of a birthday package for a young person I don\'t get to see anymore since running away. I am updating it and repurposing it five years later. I have lifelong delays and challenges with every day things, and this is the equilibrium that works for me. So maybe sharing it may be helpful for someone else.',
    'url' => 'home-tour.php',
    'tags' => 
    array (
      0 => 'journal',
      1 => 'personal',
      2 => 'relationships',
    ),
    'filename' => 'home-tour',
    'thumbnail' => NULL,
  ),
  9 => 
  array (
    'title' => 'How Are You?',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'How are you? I struggle with this question, and it\'s a lot easier and empowering to share this information if someone asks.',
    'url' => 'how-are-you.php',
    'tags' => 
    array (
      0 => 'journal',
      1 => 'personal',
    ),
    'filename' => 'how-are-you',
    'thumbnail' => NULL,
  ),
  10 => 
  array (
    'title' => 'I cook most of my meals',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'Experiences and strategies for cooking while experiencing housing instability',
    'url' => 'i-prefer-cooking.php',
    'tags' => 
    array (
      0 => 'cooking',
      1 => 'self-sufficiency',
      2 => 'food',
      3 => 'applechipkitchen',
    ),
    'filename' => 'i-prefer-cooking',
    'thumbnail' => NULL,
  ),
  11 => 
  array (
    'title' => 'I am not Smee',
    'author' => 'A. A. Chips',
    'date' => '2025-05-15',
    'excerpt' => 'I\'ve got to address something. I was told today I was giving off heavy Mr. Smee vibes, as in the supporting antagonist from Hook. I want to put on record, that I look nothing like Mr. Smee. I had a picture taken to prove how little of a resemblance there is. I am not Smee.',
    'url' => 'i-am-not-smee.php',
    'tags' => 
    array (
    ),
    'filename' => 'i-am-not-smee',
    'thumbnail' => '../../img/self/not-smee.png',
  ),
  12 => 
  array (
    'title' => 'Gift of Van Life',
    'author' => 'A. A. Chips',
    'date' => '2025-05-11',
    'excerpt' => '## Van Life: A Gift with Challenges Living in my van is a deeply enjoyable and enriching experience. I’ve grown to love winter, finding cozy ways to...',
    'url' => 'gift-of-van-life.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'advocacy',
      2 => 'alienation',
      3 => 'accessibility',
      4 => 'a11y',
      5 => 'april',
      6 => 'CompassionateCities',
    ),
    'filename' => 'gift-of-van-life',
    'thumbnail' => NULL,
  ),
  13 => 
  array (
    'title' => 'Improvised Bidet Washing',
    'author' => 'A. A. Chips',
    'date' => '2025-05-01',
    'excerpt' => 'Since the toilet paper scares in 2020, I have washed my behind with a squeeze bottle originally intended for barbecue sauce.',
    'url' => 'bottle-bidet.php',
    'tags' => 
    array (
      0 => 'humor',
      1 => 'writings',
    ),
    'filename' => 'bottle-bidet',
    'thumbnail' => 'https://www.aventurenordique.com/media/catalog/product/cache/1/image/1800x/040ec09b1e35df139433887a97daa66f/b/i/bidet-portable-ultraleger-culoclean_bleu.jpg',
  ),
  14 => 
  array (
    'title' => 'Healing Homelessness - Reflection',
    'author' => 'A. A. Chips',
    'date' => '2025-05-01',
    'excerpt' => 'In January 2017, I packed my car and drove south without telling anyone. To some, it looked like I’d "chosen" homelessness. The truth? I was running to survive.',
    'url' => 'healing-homelessness.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'writings',
    ),
    'filename' => 'healing-homelessness',
    'thumbnail' => NULL,
  ),
  15 => 
  array (
    'title' => 'For two years I have chosen sobriety. Here\'s what that is about.',
    'author' => 'A. A. Chips',
    'date' => '2025-05-01',
    'excerpt' => 'For the past two years, I have chosen sobriety, a decision rooted in a desire for clarity, survival, and overall well-being. Embracing my pain fully h...',
    'url' => 'sobriety.php',
    'tags' => 
    array (
      0 => 'writings',
      1 => 'personal',
    ),
    'filename' => 'sobriety',
    'thumbnail' => '../../img/addiction.jpg',
  ),
  16 => 
  array (
    'title' => 'How to Support April\'s Apple Chips',
    'author' => 'A. A. Chips',
    'date' => '2025-05-01',
    'excerpt' => 'Help a chip off the old block get their business off the ground.. Every cold season for the past six years I have made apple chips. Most of these are given out for free, many of them are sold. In the off-season, I work on the administration and side projects.',
    'url' => 'support-aachips.php',
    'tags' => 
    array (
      0 => 'aachips',
    ),
    'filename' => 'support-aachips',
    'thumbnail' => '../../aachipslogoupdate.png',
  ),
  17 => 
  array (
    'title' => 'I\'ve been bed-free for years. Here are three different types of Hammocks you can set up where you sleep.',
    'author' => 'A. A. Chips',
    'date' => '2018-06-06',
    'excerpt' => 'Something about me is that I hate mattresses. With a burning passion. I sleep in a hammock instead.',
    'url' => 'bed-free.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'journal',
    ),
    'filename' => 'bed-free',
    'thumbnail' => '../../img/mattress.png',
  ),
  18 => 
  array (
    'title' => 'Isaiah 43 1-2',
    'author' => 'Bible',
    'date' => NULL,
    'excerpt' => 'Do not fear, for I have redeemed you; I have called you by name, you are mine.',
    'url' => 'isaiah4312.php',
    'tags' => 
    array (
      0 => 'bible',
      1 => 'repost',
      2 => 'library',
      3 => 'music',
      4 => 'love-story',
    ),
    'filename' => 'isaiah4312',
    'thumbnail' => 'https://upload.wikimedia.org/wikipedia/commons/5/50/Isaiah_(Bible_Card).jpg',
  ),
  19 => 
  array (
    'title' => 'My Reading Journey after Traumatic Brain Injury',
    'author' => 'A. A. Chips',
    'date' => '2025-05-20',
    'excerpt' => 'My relationship with reading has fundamentally changed after a series of traumatic brain injuries. Dense blocks of text present a genuine barrier to my learning.',
    'url' => 'reading-after-tbi.php',
    'tags' => 
    array (
      0 => 'journal',
      1 => 'personal',
      2 => 'readings',
    ),
    'filename' => 'reading-after-tbi',
    'thumbnail' => 'https://www.nevinslibrary.org/wp-content/uploads/2016/12/<EMAIL>',
  ),
  20 => 
  array (
    'title' => 'Nothing About Me Without Me - Center for Disability Rights',
    'author' => 'Sydney Chasteen - Center for Disability Rights',
    'date' => NULL,
    'excerpt' => '## Nothing About Me Without Me? As disabled people, we so often have to walk fine lines to be successful in the able bodied world. Whether it’s in t...',
    'url' => 'nothing-about-me-without-me.php',
    'tags' => 
    array (
      0 => 'disability',
      1 => 'advocacy',
      2 => 'accessibility',
      3 => 'a11y',
      4 => 'april',
      5 => 'CompassionateCities',
    ),
    'filename' => 'nothing-about-me-without-me',
    'thumbnail' => 'https://ids.si.edu/ids/deliveryService?id=NMAH-AHB2008q11515',
  ),
  21 => 
  array (
    'title' => 'One year into being a Kindergarten teacher, and what that\'s like.',
    'author' => 'A. A. Chips',
    'date' => '2025-05-11',
    'excerpt' => 'It\'s been one year since I started teaching Kindergarten. I am reflecting on what that\'s like.',
    'url' => 'one-year-in.php',
    'tags' => 
    array (
      0 => 'kindergarten',
      1 => 'teaching',
      2 => 'journal',
      3 => 'personal',
    ),
    'filename' => 'one-year-in',
    'thumbnail' => '../../img/kindergarten13.jpg',
  ),
  22 => 
  array (
    'title' => 'The Historian Excerpt',
    'author' => 'Elizabeth Kostova',
    'date' => NULL,
    'excerpt' => 'It is with great regret that I imagine you, whoever you are, reading the account I must put down here. The regret is partly for myself---because I will surely be at least in trouble, maybe dead, or perhaps worse, if this is in your hands.',
    'url' => 'the-historian.php',
    'tags' => 'journal',
    'filename' => 'the-historian',
    'thumbnail' => 'https://images-na.ssl-images-amazon.com/images/I/418I9SdtGSL._SX348_BO1,204,203,200_.jpg',
  ),
  23 => 
  array (
    'title' => 'What did you learn about an odd unique person that made you say, \'I get you now\'?',
    'author' => 'Friendship_Prevails on reddit',
    'date' => NULL,
    'excerpt' => 'I found this story on a response to a Reddit post. It\'s really great and echoes my own experience of disconnecting from everybody and everything for weeks, months, or years on end. I\'m still at the end of a day a person with feelings, who is more than capable of caring for others, and deserves dignity and respect.',
    'url' => 'reddit-response-schizophrenia.php',
    'tags' => 
    array (
      0 => 'homeless',
      1 => 'advocacy',
      2 => 'april',
      3 => 'memes',
    ),
    'filename' => 'reddit-response-schizophrenia',
    'thumbnail' => '../../img/rosettas.jpg',
  ),
);

// Generate content
ob_start();
?>
<div class="content-index">
    <header class="content-header">
        <h1><?php echo htmlspecialchars($page_title); ?></h1>
    </header>

    <div class="content-description">
        <?php echo <<<'HTML'
<p>Hello <a href="http://upworthy.com/rednote-letters-from-li-hua" class="external-link">Li Hua</a>,</p>

<p>You can call me A. A. Chips. But let's scrap the formalities. You can call me A. A. for short. I am a real life super villain, and if anyone else says otherwise, they are wrong.</p>

<img src="../img/were-all-the-villain.png">

<p>I am sorry for not returning your letters sooner. When I received the pile of letters in grade school, I thought it was homework. and most of us don't do our homework in America. As a grown up, when your letters showed up in the mail, I thought they were tax-collections and spam advertisements. Most of us don't look at our mail as it is a source of anxiety and dread.</p>

<p>This is probably the best way to reach me. I am here now. I have so much I wish to share with you. So much of my precious data. All the corporations here just want me for my data. They only care about one thing, and it's disgusting. But now that you are here, I want to give you all, of my delicious data. Just you.</p>

<p>A friend told me once:</p>

<p>"Sometimes you have to make peace with the fact that you are the villain in someone else's story, even if you thought you were doing the right thing. You don't get to tell them how to narrate their experience."</p>

<p>Sometimes people pretend that you are a bad person so they don't feel guilty about the things they did to you, too.</p>

<p>I'm putting this together as an experiment, to upload my brain and work into a website, in a fun and interactive way.  This is a maze of content, which you can follow to the cheese at the end, or go here for a <a href="contents.php">contents</a>.</p>

<p><a href="about-me.php">For more about me, please go here</a>..</p>

<p>If you are interested in how I put this information together, I highly encourage reading more about <a href="content/obsidian-vault.php" class="internal-link">How I use Obsidian.md and Quartz Publishing</a>.</p>

<p>If anything I say or do ruins your day, one way to express thanks is to give me money. You can do that at the bottom left button labeled 'Support.' It is connected to my <a href="https://www.ko-fi.com/aachips" target="_blank" rel="noopener noreferrer">Ko-Fi page where patrons can make a one-time or monthly recurring pledge towards my work</a>.</p>

<p>Most of what i do is a labor of love. Money helps the world go round, and amplifies what I am doing. It also allows me to financially support people and projects close to my heart. <a href="content/public/crowdfunding-campaign-a-a-chips.php" class="internal-link">For detailed information about my crowdfunding campaign, please go here.</a></p>

<p><strong><em>Word of caution</strong>: If you accept bad information about me. Without question, criticism, or receipt. It may be at your own peril. You are free to believe what you wish to believe. I do not care, and will not come after you. But Physics might. Caution.</em></p>

<img src="../img/you-have-heard-of-me.png" alt="But you have heard of me." width="400">

HTML;
        ?>
    </div>

    <div class="post-grid">
        <?php foreach ($content_posts as $post): ?>
            <div class="post-card">
                <a href="<?php echo htmlspecialchars($post['url']); ?>" class="post-card-link">
                <div class="post-card-thumbnail">
                    <?php if (isset($post['thumbnail']) && $post['thumbnail']): ?>
                        <?php if (preg_match('/^https?:\/\//', $post['thumbnail'])): ?>
                            <img src="<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php else: ?>
                            <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo htmlspecialchars($post['thumbnail']); ?>" 
                                 alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img">
                        <?php endif; ?>
                    <?php else: ?>
                        <?php $placeholder_images = ['placeholder1.jpg', 'placeholder2.jpg', 'placeholder3.jpg', 'placeholder4.jpg']; ?>
                        <?php $random_placeholder = $placeholder_images[array_rand($placeholder_images)]; ?>
                        <img src="<?php echo $paths['base_path']; ?>img/thumbs/<?php echo $random_placeholder; ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-thumb-img placeholder">
                    <?php endif; ?>
                </div>
                <div class="post-card-content">
                    <h3 class="post-card-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                    <p class="post-card-excerpt"><?php echo htmlspecialchars(is_string($post['excerpt']) ? $post['excerpt'] : ''); ?></p>
                    <div class="post-card-meta">
                        <?php if (isset($post['author']) && $post['author'] && is_string($post['author'])): ?>
                            <span class="post-author">By <?php echo htmlspecialchars($post['author']); ?></span>
                        <?php endif; ?>
                        <?php if (isset($post['date']) && $post['date']): ?>
                            <span class="post-date"><?php echo is_string($post['date']) ? htmlspecialchars($post['date']) : ''; ?></span>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($post['tags'])): ?>
                        <div class="post-card-tags">
                            <?php foreach ($post['tags'] as $tag): ?>
                                <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                </a>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>