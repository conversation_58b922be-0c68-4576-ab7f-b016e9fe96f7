<?php
// Auto-generated blog post
// Source: liberation-seder.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Liberation Seder Documentary';
$meta_description = 'On April 26, 2016, over 500 young American Jews risked arrest in five cities during Passover to declare "Dayenu! - Enough!" against American Jewish support for the occupation.';
$meta_keywords = 'jewish, liberation, seder, passover, occupation, activism, resistance, documentary, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://cdn.stayhappening.com/events5/banners/3bfc43fb5de8428b8c333f8e26cd9beaf70bfddc29c69c4e7b6e7d7256805b00-rimg-w1200-h634-gmir.jpg?v=1709471640
../../img/sea-part.jpg:';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Liberation Seder Documentary',
  'author' => 'IfNotNow',
  'tags' => 
  array (
    0 => 'jewish',
    1 => 'liberation',
    2 => 'seder',
    3 => 'passover',
    4 => 'occupation',
    5 => 'activism',
    6 => 'resistance',
    7 => 'documentary',
  ),
  'date' => '2016-04-26',
  'source' => 'https://www.youtube.com/watch?v=mg-lMMfWumI',
  'excerpt' => 'On April 26, 2016, over 500 young American Jews risked arrest in five cities during Passover to declare "Dayenu! - Enough!" against American Jewish support for the occupation.',
  'thumbnail' => 'https://cdn.stayhappening.com/events5/banners/3bfc43fb5de8428b8c333f8e26cd9beaf70bfddc29c69c4e7b6e7d7256805b00-rimg-w1200-h634-gmir.jpg?v=1709471640
../../img/sea-part.jpg:',
  'source_file' => 'content\\judaism\\liberation-seder.md',
);

// Raw content
$post_content = '<iframe width="560" height="315" src="https://www.youtube.com/embed/mg-lMMfWumI?si=XEkLGMh30p7GZ39N" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe># IfNotNow <a href="https://www.youtube.com/hashtag/liberationseder" class="external-link">#LiberationSeder</a> Documentary

<h1>IfNotNow: Liberation Seder Documentary</h1>

<img src="https://yt3.ggpht.com/ytc/AIdro_ne1M_ch4KNPphrY9ztkKjepK3GaHTIvm_zpAKZexHZnQ=s88-c-k-c0x00ffffff-no-rj" alt="IfNotNow" width="200">

<p><a href="https://www.youtube.com/@ifnotnow72" class="external-link">IfNotNow YouTube Channel</a></p>
<p>990 subscribers</p>

<p>---</p>

<h2>A Call for Liberation</h2>

<p>On April 26, 2016, over 500 young American Jews risked arrest in five cities during Passover to declare "Dayenu! - Enough!" against American Jewish support for the occupation. This historic event is captured in the IfNotNow Liberation Seder documentary, which you can watch <a href="https://www.youtube.com/watch?v=mg-lMMfWumI" class="external-link">here</a>.</p>
<h3>Bringing Family to the Movement</h3>

<p>The documentary opens with a personal reflection, where an individual lights candles to symbolically include their family in California, both in spirit and politics, as they participate in the Liberation Seder. This act underscores the importance of carrying forward our ancestors\' dignity in the fight for collective liberation.</p>
<h3>Challenging the Occupation</h3>

<p>The film highlights the urgent need to confront the moral crisis of occupation. It calls on Jewish leaders to acknowledge this crisis and join the fight for freedom and dignity for all, emphasizing that Jewish liberation cannot happen without Palestinian liberation.</p>
<h3>Facing Resistance</h3>

<p>The documentary also addresses internal challenges, such as smear campaigns and censorship against those who speak out. It depicts the resilience of activists who refuse to be silenced and continue to demand justice.</p>
<h3>A Generation\'s Promise</h3>

<p>The message is clear: this generation is committed to transforming the community\'s support for the occupation into a movement for universal freedom and dignity. The film ends with a powerful call to action: IfNotNow, when?</p>

<p>For more information, join IfNotNow\'s training or learn how you can help caption and translate this powerful video to reach a wider audience</p>

<ul><li>Edited by Rafael Shimunov. Clips of Israelis and Palestinians used under Creative Commons license from Sherez and Dana Shihadah.</li></ul>

<p><em>Subtitles by the Amara.org community</em></p>

<p>---</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>