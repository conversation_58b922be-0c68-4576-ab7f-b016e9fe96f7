<?php
// Auto-generated blog post
// Source: dont-want-kids.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'List of things to say when someone asks why you don\'t want kids';
$meta_description = 'Reposted from nerdfighterwhatevernumbers on Pinterest.com';
$meta_keywords = 'humor, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../img/why-i-dont-want-kids.png';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'List of things to say when someone asks why you don\'t want kids',
  'author' => 'nerdfighterwhatevernumbers on Pinterest',
  'tags' => 
  array (
    0 => 'humor',
  ),
  'excerpt' => 'Reposted from nerdfighterwhatevernumbers on Pinterest.com',
  'categories' => 
  array (
    0 => 'Humor',
  ),
  'thumbnail' => '../../img/why-i-dont-want-kids.png',
  'source_file' => 'content\\humor\\dont-want-kids.md',
);

// Raw content
$post_content = '<p>Reposted from nerdfighterwhatevernumbers on Pinterest.com</p>

<ul><li>I promised my firstborn to a witch and really don\'t want to make good on the deal</li>
<p><li> Well you can have them FOR me if it\'s that big a deal to you</li></p>
<p><li>I don\'t think I could get a good price for em on the black market</li></p>
<p><li> Fight me Helen</li></p>
<p><li>I can\'t be a better parent than Angelina Jolie so why even bother</li></p>
<p><li>That\'s my nindo. My ninja way.</li></p>
<p><li>I literally JUST sat down</li></p>
<p><li>Recite "The Highway Man" from Over the Garden Wall</li></p>
<p><li>Kids? What are those? I don\'t understand. What are these you OH GRAVY WHAT IS THAT !?</li></p>
<p><li>Oohhh no, I\'ve seen Disney movies, I know what happens to mothers</li></p>
<p><li>Centipedes? In my vagina?</li></p>
<p><li><em>Angrily</em> YOU SEE !? This is just like that episode of Spongebob! + <em>insert the plot of any episode of Spongebob in excruciating detail</em></li></p>
<p><li>I heard they\'re .. you know .. itchy. Like, as soon as you have a kid  Just totally itchy. Everything.</li></p>
<p><li>I\'m an Aries</li></p>
<p><li>Well, we already got an even number so .. <em>shrug</em></li></p>
<p><li>I must first capture the Avatar to regain my honor</li></p>
<p><li>I\'m allergic</li></p>
<p>That\'s just what the communists want!</p>
<p><li>I\'ve been dead for seven years</li></p>
<p><li>Santa didn\'t bring me one last Christmas, so I guess it\'s no meant to e</li></p>
<p><li>I\'m afraid they\'ll have bad taste in memes</li></p>
<p><li>It would be unfair to my cat</li></p>
<p><li>I\'m chaotic neutral</li></p>
<p><li><em>long farting noise lasting at least 45 seconds</em></li></p>
<p><li>"I don\'t want to have children, I want to stay single, and let my hair flow</li></ul></p>
<p>in the wind as I ride through the glen firing arrows into the sunset."</p>

<img src="../../img/why-i-dont-want-kids.png" alt="why-i-dont-want-kids.png" width="400">
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>