<?php
// Set content type to JSON
header('Content-Type: application/json');

// Include necessary files
require_once dirname(__DIR__) . '/path-helper.php';
$config = include dirname(__DIR__) . '/config.php';
$paths = initPaths($config, __FILE__);

// Function to scan content directory and build graph data
function buildGraphData($basePath) {
    $contentDir = $basePath . 'content';
    $nodes = [];
    $links = [];
    $nodeIds = [];
    $categories = [];
    
    // Scan content directory recursively
    $contentFiles = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($contentDir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    // First pass: collect all nodes
    foreach ($contentFiles as $file) {
        if ($file->getExtension() === 'php' && $file->getFilename() !== 'index.php' && $file->getFilename() !== 'debug-web.php') {
            $relativePath = str_replace('\\', '/', substr($file->getPathname(), strlen($basePath)));
            $url = '/' . $relativePath;

            // Extract category from path
            $pathParts = explode('/', $relativePath);
            $category = (count($pathParts) > 2) ? $pathParts[1] : 'default';

            // Store category for later use
            if (!in_array($category, $categories) && $category !== 'content') {
                $categories[] = $category;
            }

            // Get title and tags from file content
            $title = getPageTitle($file->getPathname());
            $tags = getPageTags($file->getPathname());
            $id = md5($relativePath); // Create unique ID

            $nodes[] = [
                'id' => $id,
                'title' => $title,
                'url' => $url,
                'category' => $category,
                'tags' => $tags,
                'weight' => 1 // Default weight
            ];

            $nodeIds[$relativePath] = $id;
        }
    }
    
    // Second pass: establish links based on related posts and shared tags
    foreach ($contentFiles as $file) {
        if ($file->getExtension() === 'php' && $file->getFilename() !== 'index.php' && $file->getFilename() !== 'debug-web.php') {
            $relativePath = str_replace('\\', '/', substr($file->getPathname(), strlen($basePath)));
            $sourceId = $nodeIds[$relativePath];

            // Parse file to find related posts
            $relatedPosts = getRelatedPosts($file->getPathname());

            foreach ($relatedPosts as $relatedPost) {
                $relatedUrl = $relatedPost['url'];
                $relatedPath = convertUrlToPath($relatedUrl, $basePath);

                if (isset($nodeIds[$relatedPath])) {
                    $targetId = $nodeIds[$relatedPath];

                    // Add link if not already exists
                    $linkExists = false;
                    foreach ($links as $link) {
                        if (($link['source'] === $sourceId && $link['target'] === $targetId) ||
                            ($link['source'] === $targetId && $link['target'] === $sourceId)) {
                            $linkExists = true;
                            break;
                        }
                    }

                    if (!$linkExists && $sourceId !== $targetId) {
                        $links[] = [
                            'source' => $sourceId,
                            'target' => $targetId,
                            'value' => 2, // Higher value for explicit related posts
                            'type' => 'related'
                        ];
                    }
                }
            }
        }
    }

    // Third pass: establish links based on shared tags
    for ($i = 0; $i < count($nodes); $i++) {
        for ($j = $i + 1; $j < count($nodes); $j++) {
            $node1 = $nodes[$i];
            $node2 = $nodes[$j];

            // Check for shared tags
            $sharedTags = array_intersect($node1['tags'], $node2['tags']);
            if (!empty($sharedTags)) {
                // Check if link already exists
                $linkExists = false;
                foreach ($links as $link) {
                    if (($link['source'] === $node1['id'] && $link['target'] === $node2['id']) ||
                        ($link['source'] === $node2['id'] && $link['target'] === $node1['id'])) {
                        $linkExists = true;
                        break;
                    }
                }

                if (!$linkExists) {
                    $links[] = [
                        'source' => $node1['id'],
                        'target' => $node2['id'],
                        'value' => count($sharedTags), // Value based on number of shared tags
                        'type' => 'tag',
                        'sharedTags' => $sharedTags
                    ];
                }
            }

            // Check for same category (weaker connection)
            if ($node1['category'] === $node2['category'] && $node1['category'] !== 'default') {
                // Check if link already exists
                $linkExists = false;
                foreach ($links as $link) {
                    if (($link['source'] === $node1['id'] && $link['target'] === $node2['id']) ||
                        ($link['source'] === $node2['id'] && $link['target'] === $node1['id'])) {
                        $linkExists = true;
                        break;
                    }
                }

                if (!$linkExists) {
                    $links[] = [
                        'source' => $node1['id'],
                        'target' => $node2['id'],
                        'value' => 0.5, // Weaker connection for same category
                        'type' => 'category'
                    ];
                }
            }
        }
    }
    
    // Update node weights based on number of connections
    $nodeLinkCounts = [];
    foreach ($links as $link) {
        if (!isset($nodeLinkCounts[$link['source']])) {
            $nodeLinkCounts[$link['source']] = 0;
        }
        if (!isset($nodeLinkCounts[$link['target']])) {
            $nodeLinkCounts[$link['target']] = 0;
        }
        $nodeLinkCounts[$link['source']]++;
        $nodeLinkCounts[$link['target']]++;
    }
    
    foreach ($nodes as &$node) {
        if (isset($nodeLinkCounts[$node['id']])) {
            $node['weight'] = min(3, $nodeLinkCounts[$node['id']] / 5 + 1);
        }
    }
    
    return [
        'nodes' => $nodes,
        'links' => $links,
        'categories' => $categories
    ];
}

// Helper function to extract page title from PHP file
function getPageTitle($filePath) {
    $content = file_get_contents($filePath);
    if (preg_match('/\$page_title\s*=\s*[\'"](.+?)[\'"]/s', $content, $matches)) {
        $title = $matches[1];
        // Clean up multi-line titles
        $title = preg_replace('/\s*\n\s*"[^"]*"\s*;?$/m', '', $title);
        return $title;
    }
    return basename($filePath, '.php');
}

// Helper function to extract tags from PHP file
function getPageTags($filePath) {
    $content = file_get_contents($filePath);
    $tags = [];

    // Look for tags in post_data array
    if (preg_match('/\$post_data\s*=\s*\[(.*?)\];/s', $content, $matches)) {
        $postDataStr = $matches[1];
        if (preg_match('/[\'"]tags[\'"]\s*=>\s*\[(.*?)\]/s', $postDataStr, $tagMatches)) {
            $tagsStr = $tagMatches[1];
            preg_match_all('/[\'"]([^\'"]+)[\'"]/', $tagsStr, $tagValues);
            $tags = array_merge($tags, $tagValues[1]);
        }
    }

    // Also look for direct tags variable
    if (preg_match('/\$tags\s*=\s*\[(.*?)\];/s', $content, $matches)) {
        $tagsStr = $matches[1];
        preg_match_all('/[\'"]([^\'"]+)[\'"]/', $tagsStr, $tagValues);
        $tags = array_merge($tags, $tagValues[1]);
    }

    return array_unique($tags);
}

// Helper function to extract related posts from PHP file
function getRelatedPosts($filePath) {
    $content = file_get_contents($filePath);
    $relatedPosts = [];
    
    if (preg_match('/\$related_posts\s*=\s*\[(.*?)\];/s', $content, $matches)) {
        $relatedPostsStr = $matches[1];
        
        // Extract individual related post entries
        preg_match_all('/\[\s*\'title\'\s*=>\s*\'(.*?)\'\s*,\s*\'url\'\s*=>\s*(.*?)\s*,/s', $relatedPostsStr, $postMatches, PREG_SET_ORDER);
        
        foreach ($postMatches as $match) {
            $url = trim($match[2]);
            // Clean up URL - remove variables and quotes
            $url = preg_replace('/PathHelper::getCategoryIndexPath\([\'"](.+?)[\'"]\)/', 'content/$1/index.php', $url);
            $url = preg_replace('/[\'"](.*?)[\'"]/s', '$1', $url);
            $url = preg_replace('/\s*\.\s*\$[a-zA-Z_]+\s*\.\s*/', '', $url);
            
            $relatedPosts[] = [
                'title' => $match[1],
                'url' => $url
            ];
        }
    }
    
    return $relatedPosts;
}

// Helper function to convert URL to file path
function convertUrlToPath($url, $basePath) {
    // Remove base URL if present
    $url = preg_replace('/^https?:\/\/[^\/]+/', '', $url);
    
    // Remove leading slash and add content/ if needed
    $url = ltrim($url, '/');
    if (!preg_match('/^content\//', $url)) {
        $url = 'content/' . $url;
    }
    
    return $url;
}

// Build and output graph data
$graphData = buildGraphData($paths['base_path']);
echo json_encode($graphData);