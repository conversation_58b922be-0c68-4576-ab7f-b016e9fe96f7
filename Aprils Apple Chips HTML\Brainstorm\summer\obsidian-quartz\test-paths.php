<?php
/**
 * Path Constants Test File
 * Demonstrates how the new path system works from different directory levels
 */

// Load the path helper
require_once 'path-helper.php';

// Load configuration
$config = include 'config.php';

// Initialize paths for this file (root level)
$paths = initPaths($config, __FILE__);

echo "<h1>Path Constants Test</h1>\n";
echo "<h2>From Root Directory</h2>\n";
echo "<ul>\n";
echo "<li>Base Path: " . htmlspecialchars($paths['base_path']) . "</li>\n";
echo "<li>CSS Path: " . htmlspecialchars($paths['css_path']) . "</li>\n";
echo "<li>JS Path: " . htmlspecialchars($paths['js_path']) . "</li>\n";
echo "<li>Images Path: " . htmlspecialchars($paths['images_path']) . "</li>\n";
echo "<li>Content Path: " . htmlspecialchars($paths['content_path']) . "</li>\n";
echo "<li>Current Depth: " . $paths['current_depth'] . "</li>\n";
echo "</ul>\n";

// Test from content directory depth
echo "<h2>Simulated from Content Directory (depth 1)</h2>\n";
PathHelper::setCurrentDepth(1);
$contentPaths = PathHelper::getPathConstants();
echo "<ul>\n";
echo "<li>Base Path: " . htmlspecialchars($contentPaths['base_path']) . "</li>\n";
echo "<li>CSS Path: " . htmlspecialchars($contentPaths['css_path']) . "</li>\n";
echo "<li>JS Path: " . htmlspecialchars($contentPaths['js_path']) . "</li>\n";
echo "<li>Images Path: " . htmlspecialchars($contentPaths['images_path']) . "</li>\n";
echo "<li>Template Path: " . htmlspecialchars($contentPaths['template_path']) . "</li>\n";
echo "<li>Current Depth: " . $contentPaths['current_depth'] . "</li>\n";
echo "</ul>\n";

// Test from subdirectory depth
echo "<h2>Simulated from Subdirectory (depth 2)</h2>\n";
PathHelper::setCurrentDepth(2);
$subPaths = PathHelper::getPathConstants();
echo "<ul>\n";
echo "<li>Base Path: " . htmlspecialchars($subPaths['base_path']) . "</li>\n";
echo "<li>CSS Path: " . htmlspecialchars($subPaths['css_path']) . "</li>\n";
echo "<li>JS Path: " . htmlspecialchars($subPaths['js_path']) . "</li>\n";
echo "<li>Images Path: " . htmlspecialchars($subPaths['images_path']) . "</li>\n";
echo "<li>Template Path: " . htmlspecialchars($subPaths['template_path']) . "</li>\n";
echo "<li>Current Depth: " . $subPaths['current_depth'] . "</li>\n";
echo "</ul>\n";

// Test convenience functions
echo "<h2>Convenience Functions</h2>\n";
PathHelper::setCurrentDepth(0); // Reset to root
echo "<ul>\n";
echo "<li>cssPath('style.css'): " . htmlspecialchars(cssPath('style.css')) . "</li>\n";
echo "<li>jsPath('script.js'): " . htmlspecialchars(jsPath('script.js')) . "</li>\n";
echo "<li>imgPath('logo.png'): " . htmlspecialchars(imgPath('logo.png')) . "</li>\n";
echo "<li>contentPath('index.php'): " . htmlspecialchars(contentPath('index.php')) . "</li>\n";
echo "</ul>\n";

// Test internal link resolution
echo "<h2>Internal Link Resolution</h2>\n";
echo "<ul>\n";
echo "<li>Simple page: " . htmlspecialchars(PathHelper::resolveInternalLink('about-me')) . "</li>\n";
echo "<li>Category page: " . htmlspecialchars(PathHelper::resolveInternalLink('alienation/16-truths-alienation')) . "</li>\n";
echo "<li>Category index: " . htmlspecialchars(PathHelper::getCategoryIndexPath('humor')) . "</li>\n";
echo "</ul>\n";

echo "<h2>Image Path Examples</h2>\n";
echo "<ul>\n";
echo "<li>From root: " . htmlspecialchars(PathHelper::getImageUrl('logo.png')) . "</li>\n";
PathHelper::setCurrentDepth(1);
echo "<li>From content dir: " . htmlspecialchars(PathHelper::getImageUrl('logo.png')) . "</li>\n";
PathHelper::setCurrentDepth(2);
echo "<li>From subdirectory: " . htmlspecialchars(PathHelper::getImageUrl('logo.png')) . "</li>\n";
echo "</ul>\n";

echo "<p><strong>Benefits of this system:</strong></p>\n";
echo "<ul>\n";
echo "<li>✅ No more manual path adjustments in build.php</li>\n";
echo "<li>✅ Images work from any directory level</li>\n";
echo "<li>✅ CSS and JS paths are always correct</li>\n";
echo "<li>✅ Template includes work from anywhere</li>\n";
echo "<li>✅ Prevents content/content/content loops</li>\n";
echo "<li>✅ Internal links resolve correctly</li>\n";
echo "<li>✅ Category indexes have correct paths</li>\n";
echo "</ul>\n";
?>
