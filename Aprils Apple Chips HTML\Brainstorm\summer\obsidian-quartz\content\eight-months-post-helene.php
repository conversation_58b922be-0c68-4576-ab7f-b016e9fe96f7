<?php
// Auto-generated blog post
// Source: eight-months-post-helene.md

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = 'Eight months after Hurricane <PERSON><PERSON> hit us. I wasn\'t impacted hard. Everyone around me was.';
$meta_description = 'Reflections on witnessing community impact and recovery after Hurricane <PERSON><PERSON>';
$meta_keywords = 'climate, disasterrelief, community, masscommunication, personal, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [
    ['title' => 'Alienation', 'url' => '../content/alienation/index.php', 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => '../content/climate/index.php', 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => '../content/humor/index.php', 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => '../content/inspiration/index.php', 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => '../content/journal/index.php', 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => '../content/judaism/index.php', 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => '../content/kitchen/index.php', 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Street', 'url' => '../content/street/index.php', 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => '../content/writings/index.php', 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Eight months after Hurricane Helene hit us. I wasn\'t impacted hard. Everyone around me was.',
  'date' => '2025-05-20',
  'tags' => 
  array (
    0 => 'climate',
    1 => 'disasterrelief',
    2 => 'community',
    3 => 'masscommunication',
    4 => 'personal',
  ),
  'excerpt' => 'Reflections on witnessing community impact and recovery after Hurricane Helene',
  'source_file' => 'content\\eight-months-post-helene.md',
);

// Raw content
$post_content = '<h2>Reflection Questions</h2>

<ul><li>What does it mean to be relatively unaffected by a disaster while those around you suffer?</li>

<p><li>How does witnessing others\' hardship change your perspective on community and resilience?</li></p>

<p><li>What responsibilities do those less impacted have toward those more severely affected?</li></p>

<p><li>How has the community changed in the months following the hurricane?</li></p>



<h2>Initial Thoughts</h2>

<p>Natural disasters often impact communities unevenly, creating complex dynamics of privilege, responsibility, and survivor\'s guilt. This document can explore:</p>

<p><li>The experience of being less affected while witnessing others\' suffering</li></p>

<p><li>The evolution of community response over the months following a disaster</li></p>

<p><li>The long-term impacts that remain visible after media attention fades</li></p>

<p><li>Personal reflections on climate change and increasing extreme weather events</li></p>



<h2>Personal Experience</h2>

<p>[Space for personal reflections on your experience during and after Hurricane Helene]</p>



<h2>Community Response</h2>

<p>[Space to document how the community has responded and recovered over time]</p>



<h2>Ongoing Challenges</h2>

<p>[Space to discuss issues that persist eight months after the disaster]</p>



<h2>Related Content</h2>

<p><li><a href="if-you-have-never-been-in-a-hurricane-nappy-thoughts.php" class="internal-link">If you have never been in a hurricane - Nappy Thoughts</a></li></p>

<p><li><a href="cultivating-community-and-solidarity.php" class="internal-link">Cultivating Community and Solidarity</a></li></p>

<p><li><a href="the-swannanoa-mulch-fire.php" class="internal-link">The Swannanoa Mulch Fire</a></li></p>

<p><li><a href="rethinking-digital-ecosystems-a-call-for-ecological-literacy-in-tech.php" class="internal-link">Rethinking Digital Ecosystems - A Call for Ecological Literacy in Tech</a></li></ul></p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>