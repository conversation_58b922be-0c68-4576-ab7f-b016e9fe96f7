<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Donation Modal Test - A. A. Chips</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <h1>Donation Modal Test Page</h1>
        <p>This page is for testing the donation modal system. The modal should appear after visiting 3-5 pages.</p>
        
        <div class="test-controls">
            <h2>Test Controls</h2>
            <button onclick="clearStorage()">Clear Visit Counter</button>
            <button onclick="showVisitCount()">Show Visit Count</button>
            <button onclick="forceModal()">Force Show Modal</button>
            <button onclick="resetModalTimer()">Reset Modal Timer</button>
        </div>
        
        <div class="test-links">
            <h2>Test Links (Click to increase page count)</h2>
            <ul>
                <li><a href="content/index.php">Home</a></li>
                <li><a href="content/humor/index.php">Humor</a></li>
                <li><a href="content/alienation/index.php">Alienation</a></li>
                <li><a href="content/inspiration/index.php">Inspiration</a></li>
                <li><a href="content/street/index.php">Street</a></li>
            </ul>
        </div>
        
        <div id="test-output">
            <h2>Test Output</h2>
            <div id="output-content"></div>
        </div>
    </div>

    <!-- Include the donation modal script -->
    <script src="js/donation-modal.js"></script>
    
    <script>
        function clearStorage() {
            localStorage.removeItem('aachips_page_visits');
            localStorage.removeItem('aachips_modal_shown');
            updateOutput('Storage cleared. Visit count reset to 0.');
        }
        
        function showVisitCount() {
            const visits = localStorage.getItem('aachips_page_visits') || '0';
            const lastShown = localStorage.getItem('aachips_modal_shown');
            const lastShownDate = lastShown ? new Date(parseInt(lastShown)).toLocaleString() : 'Never';
            updateOutput(`Current visit count: ${visits}<br>Last modal shown: ${lastShownDate}`);
        }
        
        function forceModal() {
            // Create a temporary modal instance to force show
            const modal = document.getElementById('donation-modal');
            if (modal) {
                // Trigger the modal manually
                const donationModal = new DonationModal();
                donationModal.showModal();
                updateOutput('Modal forced to show.');
            } else {
                updateOutput('Modal not found. Make sure donation-modal.js is loaded.');
            }
        }
        
        function resetModalTimer() {
            localStorage.removeItem('aachips_modal_shown');
            updateOutput('Modal timer reset. Modal can show again.');
        }
        
        function updateOutput(message) {
            const output = document.getElementById('output-content');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML = `[${timestamp}] ${message}<br>` + output.innerHTML;
        }
        
        // Show initial status
        document.addEventListener('DOMContentLoaded', function() {
            showVisitCount();
        });
    </script>
    
    <style>
        .container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            font-family: Arial, sans-serif;
        }
        
        .test-controls, .test-links {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        button {
            margin: 0.5rem;
            padding: 0.5rem 1rem;
            background: #6b5b95;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #5a4a82;
        }
        
        #test-output {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f0f0f0;
            max-height: 300px;
            overflow-y: auto;
        }
        
        #output-content {
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        ul {
            list-style-type: none;
            padding: 0;
        }
        
        li {
            margin: 0.5rem 0;
        }
        
        a {
            color: #6b5b95;
            text-decoration: none;
            padding: 0.5rem;
            display: inline-block;
            border: 1px solid #6b5b95;
            border-radius: 4px;
        }
        
        a:hover {
            background: #6b5b95;
            color: white;
        }
    </style>
</body>
</html>
