<?php
// Auto-generated blog post
// Source: middle-ground.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Middle Ground';
$meta_description = 'The dog is getting better and more comfortable with regular walks. They know they aren\'t in trouble and don\'t need to be scared. Maybe we all need that.. Tomorrow maybe I can find a chore chart to put on the fridge. Everyone is safe, but handling the aftermath of chaos differently.';
$meta_keywords = 'journal, homeless, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Middle Ground',
  'author' => 'A. A. Chips',
  'date' => '2025-05-22',
  'categories' => 
  array (
    0 => 'Journal',
  ),
  'tags' => 
  array (
    0 => 'journal',
    1 => 'homeless',
  ),
  'excerpt' => 'The dog is getting better and more comfortable with regular walks. They know they aren\'t in trouble and don\'t need to be scared. Maybe we all need that.. Tomorrow maybe I can find a chore chart to put on the fridge. Everyone is safe, but handling the aftermath of chaos differently.',
  'source_file' => 'content\\journal\\middle-ground.md',
);

// Raw content
$post_content = '<p>She mentioned scooping the cat liter and taking out the trash. It was subtle. I try my best with the mind reading. After scooping into a plastic grocery bag and putting in the trash, we get distracted chitchatting. Laughing about something. "You\'re going to wash your hands, right?" I was but got distracted. Ultimately I forgot to take the trash out. My sense of scent isn\'t like her bloodhound nose post-chemo.</p>

<p>\'I just want to be a good dog.\'</p>

<p>That night, her laughter spilled from her room between coughing fits—deep, rattling things. She’d cut back on smoking inside after the neighbors complained, but the engine damage lingered. I\'ve made it almost two years without that crap, but after daily use for almost two decades, I understand how helpful it is to some people. There is a bug going around. I wondered if I should offer my inhaler. Held back. My work hours were too few to risk getting sick.</p>

<p>She\'s stressed about to waking up early to catch the 8 bus. Then my own cough erupted—loud, involuntary. Ten minutes of choking on nothing. She tells me to quiet down so she can get to sleep. Sorry, I\'ll find a cough drop. No problem.</p>

<p>She\'s still moving around. Gets up goes downstairs. Is muttering angrily and slams the door. I have no idea what is going on. A text: _Took out the trash again. Night._ I\'m so confused. How did we fill two trash bags today? Then the recognition: Oh. The dog makes this face too.</p>

<p>Stillness. Safety. The art of being small. I just want to be a good dog.</p>

<p>Morning came with an apology--overreacted. Even at her worst, it\'s way better than the monsters from the past. The ghosts we had both outrun.</p>

<p>Then I left the shower drain clogged. A mistake. Not a war. After church service ended, the blow up text was there with the softening right after. \'I\'m not your maid.\' We\'re the same, I realized. Both afraid waiting for the other shoe to drop.</p>

<p>Money was tight. My summer starvation season means rice and canned beans; her DoorDash orders taunt me. It\'s not that I want the food, just to have the relative luxury to not penny pinch and to be able to afford groceries. We’d agreed to share food, but her diabetes might make every carb a landmine.</p>

<p>Her pretty man-friend came over—the same one she said she never wanted at the house ever again. I don\'t understand human dating and shifting boundaries. It\'s like the dog\'s pee spots: shifting, invisible, and punished retroactively.</p>

<p>The dog is getting better and more comfortable with regular walks. They know they aren\'t in trouble and don\'t need to be scared. Maybe we all need that..</p>

<p>Tomorrow maybe I can find a chore chart to put on the fridge. Everyone is safe, but handling the aftermath of chaos differently.</p>



';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>