<?php
// Auto-generated blog post
// Source: wellness-checks.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Rethinking Wellness Checks';
$meta_description = 'We all want our loved ones to be safe, and sometimes that means relying on wellness checks. But for adults with learning disabilities, these checks can have a dangerous downside – a higher risk of being hurt by law enforcement.';
$meta_keywords = 'homeless, accessibility, CompassionateCities, blog, parentalalienation, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://police-station.com/wp-content/uploads/2023/06/How-to-do-Welfare-wellness-safe-check-from-the-Police-Worldwide.png';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Rethinking Wellness Checks',
  'author' => 'A. A. Chips',
  'excerpt' => 'We all want our loved ones to be safe, and sometimes that means relying on wellness checks. But for adults with learning disabilities, these checks can have a dangerous downside – a higher risk of being hurt by law enforcement.',
  'tags' => 
  array (
    0 => 'homeless',
    1 => 'accessibility',
    2 => 'CompassionateCities',
    3 => 'blog',
    4 => 'parentalalienation',
  ),
  'categories' => 
  array (
    0 => 'Writings',
  ),
  'thumbnail' => 'https://police-station.com/wp-content/uploads/2023/06/How-to-do-Welfare-wellness-safe-check-from-the-Police-Worldwide.png',
  'source_file' => 'content\\street\\wellness-checks.md',
);

// Raw content
$post_content = '<h2><strong>Protecting Our Vulnerable: Rethinking Wellness Checks for Adults with Learning Disabilities</strong></h2>

<p>We all want our loved ones to be safe, and sometimes that means relying on wellness checks. But for adults with learning disabilities, these checks can have a dangerous downside – a higher risk of being hurt by law enforcement.</p>

<p><strong>The Problem: Misunderstandings and Overburdened Officers</strong></p>

<p>Here\'s the harsh reality: communication can break down during police interactions with people who have learning disabilities. Instructions might be confusing, responses misinterpreted as aggression, and sensory overload from flashing lights and raised voices can trigger panic.</p>

<p>This isn\'t the fault of the individuals or the officers. Police wear many hats, expected to respond to everything from traffic violations to mental health crises. They simply may not have the specialized training needed to handle situations involving learning disabilities.</p>

<p><strong>The Result: A Call for Change</strong></p>

<p>The good news is, there\'s a growing movement to address this issue. Policy changes are being proposed to prevent these tragedies:</p>

<ul><li><strong>Shifting Responsibilities:</strong> Advocates are pushing for a reallocation of resources. Social workers, trained to de-escalate situations involving people with disabilities, could be the primary responders for wellness checks initiated by the state.</li>

<p><li><strong>Specialized Teams:</strong> In some areas, co-response teams are being piloted. These combine police officers with mental health professionals, creating a more informed and supportive presence.</li></p>

<p><li><strong>Community-Based Solutions:</strong> For personal situations, involving trusted family members or friends can be the best course of action. These individuals already understand the person\'s needs and can provide a calming influence.</li></ul></p>

<p><strong>Remember, You Are Their Voice</strong></p>

<p>As caregivers, we have a crucial role to play. By understanding the risks and advocating for these policy changes, we can ensure wellness checks become true well-being checks, providing the support our loved ones need, not additional stress.</p>

<p><strong>Let\'s work together to create a system that protects our most vulnerable.</strong></p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>