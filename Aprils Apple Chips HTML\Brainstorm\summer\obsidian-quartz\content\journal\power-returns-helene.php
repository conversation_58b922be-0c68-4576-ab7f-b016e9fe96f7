<?php
// Auto-generated blog post
// Source: power-returns-helene.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Power Returns After Helene';
$meta_description = 'We get hit by tropical storms here. No one expected this. Bodies of water rising 14 feet in 12 hours. Houses with their second story flooded. Businesses in the village sunk to only their sign being visible. We don\'t know the damage. It\'s bad.';
$meta_keywords = 'journal, disasterrelief, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Power Returns After Helene',
  'date' => '2024-09-30',
  'tags' => 
  array (
    0 => 'journal',
    1 => 'disasterrelief',
  ),
  'categories' => 
  array (
    0 => 'Journal',
  ),
  'excerpt' => 'We get hit by tropical storms here. No one expected this. Bodies of water rising 14 feet in 12 hours. Houses with their second story flooded. Businesses in the village sunk to only their sign being visible. We don\'t know the damage. It\'s bad.',
  'source_file' => 'content\\journal\\power-returns-helene.md',
);

// Raw content
$post_content = '<p>We get hit by tropical storms here. No one expected this. Bodies of water rising 14 feet in 12 hours. Houses with their second story flooded. Businesses in the village sunk to only their sign being visible. We don\'t know the damage. It\'s bad. Not sure that we have the records going far enough back, but I am willing to bet this was a 1,000 year, or 10,000 year flood. These events will become more common. Teach your kids how to filter water and start a fire.</p>

<p>I am very fortunate. I am at high altitude close to the city. We have gone five days with no power, four with no water. I am able to weather that. We have had people on oxygen in long lines by the hospital. We have had homes and businesses completely destroyed. I\'ve done a drive every day to assess and witness. There were some areas completely made inaccessible by fallen trees and power lines. Particularly where the mudslides happened. This is just in our area. I do not have the stats of the entire tri-state region.</p>

<p>My phone ran out of power after the first day. Forgot to turn on Airplane Mode. We are fortunate to have a chest freezer which kept many items frozen much longer than they would otherwise. Everyday I have had to haul and carry water. The greatest luxury was having battery powered Christmas lights in the closet so I can read at night.</p>

<p>At every opportunity, neighbors came through for each other. Establishments served free meals and were gentle with others. Our neighborhood had one side with a fallen tree blocking entrance and exit out of that area. Services were not dispatched. There were thirty people congregated at and on this tree with hand saws and axes breaking it down by hand. I wish I could have gotten pictures of more.</p>

<p>It is one thing to witness events like this happen far away through the news. It is another thing to see the damage first hand. Our kids are going to grow up into a world with more severe natural disasters and resource conflicts. Please prepare them for this reality. Prepare yourself and learn how to take care of yourself when running water and electricity go away for weeks at a time. Be kind and patient with each other.</p>

<p>I am asking. If you are part of the global minority that is accustomed to consuming mindlessly non-stop. Kiss your lifestyle goodbye. Learn to live without conspicuous consumption. If you give a damn about the world your children and grandchildren will live in, be pro-active in simplifying your life and footprint. Move your investments towards things that affirm life and do not destroy it. And if you are not willing to do that. Get out of the way.</p>

<p>If you would like to support me by buying bags of apple chips, a carbon negative snack and \'chiptocurrency,\' please reach out. I am asking for 10$ a bag if deliverable in person and 15$ a bag if shipping is involved. Bulk rates are cheaper. They make great holiday gifts.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>