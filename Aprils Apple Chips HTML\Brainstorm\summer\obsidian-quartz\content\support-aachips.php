<?php
// Auto-generated blog post
// Source: support-aachips.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'How to Support April\'s Apple Chips';
$meta_description = 'Help a chip off the old block get their business off the ground.. Every cold season for the past six years I have made apple chips. Most of these are given out for free, many of them are sold. In the off-season, I work on the administration and side projects.';
$meta_keywords = 'aachips, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = $paths['base_path'] . 'img/thumbs/' . '../../aachipslogoupdate.png';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'How to Support April\'s Apple Chips',
  'tags' => 
  array (
    0 => 'aachips',
  ),
  'author' => 'A. A. Chips',
  'excerpt' => 'Help a chip off the old block get their business off the ground.. Every cold season for the past six years I have made apple chips. Most of these are given out for free, many of them are sold. In the off-season, I work on the administration and side projects.',
  'date' => '2025-05-01',
  'thumbnail' => '../../aachipslogoupdate.png',
  'source_file' => 'content\\support-aachips.md',
);

// Raw content
$post_content = '<img src="../../aachipslogoupdate.png" alt="A. A. Chips logo" width="200">

<p>Help a chip off the old block get their business off the ground..</p>

<p>Every cold season for the past six years, I have made apple chips. Most of these are given out for free, and many are sold. In the off-season, I work on administration, side projects, and sometimes take private cooking jobs. My part-time job, teaching, takes up 18 hours a week and consumes most of my energy. I have bills, aspirations, and people and places I wish to support. Here are some ways you can be the wind beneath my wings:</p>

<h3>Support Options</h3>

<p>1. <strong>Monthly Support via Ko-Fi:</strong></p>
<p>- Become a monthly supporter on our Ko-Fi page! Ko-Fi allows you to pledge a small amount, like $3-$5 per month, to show ongoing support for our business. These contributions help us cover ongoing costs and invest in future culinary endeavors. Visit our <a href="https://www.ko-fi.com/aachips" class="external-link">Ko-Fi page</a> to learn more.</p>

<p>2. <strong>One-time Donation:</strong></p>
<p>- Donate directly to our <a href="https://www.paypal.me/bbushwick" class="external-link">Paypal</a> or <a href="https://www.ko-fi.com/aachips" class="external-link">Ko-Fi campaign</a>. Ko-Fi allows monthly pledges, and maintaining a one or two dollar monthly pledge is more valuable than a one-time twenty-dollar donation.</p>

<p>3. <strong>Leave an Anonymous Tip:</strong></p>
<p>- If you’re delighted with the food and service, consider leaving an anonymous tip. This is a fantastic way to express your gratitude directly and support our team. A tip jar will be available throughout any event.</p>

<p>4. <strong>Purchase Apple Chips:</strong></p>
<p>- Buy bags of apple chips as pay-it-forward, which covers the expense of gifting free bags to others. You can also <a href="https://www.aachips.co/order" class="external-link">pre-order bags of apple chips</a> before the season starts. First come, first serve.</p>

<p>5. <strong>Hire for Cooking Services:</strong></p>
<p>- Hire me to cook for you, your family, or your event. You provide the kitchen. I also offer cooking workshops and coaching to help people become better home cooks.</p>

<p>6. <strong>Website Assistance:</strong></p>
<p>- I can help you set up, maintain, or fix your website. I work in code, as well as WordPress.</p>

<p>7. <strong>Share Your Experience:</strong></p>
<p>- Written testimonials are invaluable for our business. Share your testimonials if you enjoy our service or apple chips on our website or social media pages. Your positive feedback helps us reach new clients and build a strong reputation. <a href="content/bad-chip-testimonial-guide.php" class="internal-link">How to leave really bad apple chip testimonials</a></p>

<p>8. <strong>Wishlist Gifts:</strong></p>
<p>- If you’d like to offer a personal touch, consider a gift from our wishlist. We appreciate items like:</p>
<p>- Old electronics for tinkering and upcycling projects.</p>
<p>- Unused camping gear in good condition.</p>
<p>- Hammock-related items like hammocks, straps, stands, or accessories.</p>
<p>- Reusable water bottles, particularly Nalgene, steel, or aluminum with intact seals.</p>

<p><strong>Please Note:</strong> Gifts are completely optional and never expected. Your presence and participation are the greatest gifts of all!</p>

<p>Thank you for choosing Chef April to cater your events! Your support helps us continue doing what we love. We appreciate your understanding and support!</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>