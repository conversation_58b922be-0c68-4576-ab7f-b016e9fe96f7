<?php
// Debug file to understand path issues when accessed via web browser
echo "<h1>Path Debug Information</h1>";

echo "<h2>Server Information</h2>";
echo "<p><strong>SCRIPT_FILENAME:</strong> " . ($_SERVER['SCRIPT_FILENAME'] ?? 'Not set') . "</p>";
echo "<p><strong>DOCUMENT_ROOT:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "</p>";
echo "<p><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p><strong>Current working directory:</strong> " . getcwd() . "</p>";
echo "<p><strong>__FILE__:</strong> " . __FILE__ . "</p>";
echo "<p><strong>__DIR__:</strong> " . __DIR__ . "</p>";

echo "<h2>Path Tests</h2>";

// Test different path combinations
$paths_to_test = [
    '../../path-helper.php',
    '../../../path-helper.php',
    __DIR__ . '/../../path-helper.php',
    __DIR__ . '/../../../path-helper.php'
];

foreach ($paths_to_test as $path) {
    echo "<p><strong>$path:</strong> " . (file_exists($path) ? 'EXISTS' : 'NOT FOUND') . "</p>";
}

echo "<h2>Template Tests</h2>";

$template_paths = [
    '../../page template.htm',
    '../../../page template.htm',
    __DIR__ . '/../../page template.htm',
    __DIR__ . '/../../../page template.htm'
];

foreach ($template_paths as $path) {
    echo "<p><strong>$path:</strong> " . (file_exists($path) ? 'EXISTS' : 'NOT FOUND') . "</p>";
}

// Try to load path helper if it exists
echo "<h2>Path Helper Test</h2>";
if (file_exists('../../path-helper.php')) {
    try {
        require_once '../../path-helper.php';
        $config = include '../../config.php';
        $paths = initPaths($config, __FILE__);
        
        echo "<p><strong>Path helper loaded successfully!</strong></p>";
        echo "<p><strong>Current depth:</strong> " . $paths['current_depth'] . "</p>";
        echo "<p><strong>Base path:</strong> " . htmlspecialchars($paths['base_path']) . "</p>";
        echo "<p><strong>Template path:</strong> " . htmlspecialchars($paths['template_path']) . "</p>";
        echo "<p><strong>Template exists:</strong> " . (file_exists($paths['template_path']) ? 'YES' : 'NO') . "</p>";
        
    } catch (Exception $e) {
        echo "<p><strong>Error loading path helper:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    }
} else {
    echo "<p><strong>Path helper not found with ../../</strong></p>";
}

if (file_exists('../../../path-helper.php')) {
    echo "<p><strong>Path helper found with ../../../</strong></p>";
} else {
    echo "<p><strong>Path helper not found with ../../../</strong></p>";
}
?>
