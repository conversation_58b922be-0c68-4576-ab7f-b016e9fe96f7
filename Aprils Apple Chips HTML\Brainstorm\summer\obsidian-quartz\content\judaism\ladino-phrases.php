<?php
// Auto-generated blog post
// Source: ladino-phrases.md

// Load path helper and configuration with fallback
$pathPrefix = '../';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = '14 Ladino Phrases Every Jew Should Know - HeyAlma';
$meta_description = 'On that note of diversity, as <PERSON><PERSON> puts it, “Ladino is a language that builds bridges.” It intersects with Hebrew, Spanish, Arabic, and a whole map of countries it’s passed through. We are made stronger by using language and our commonalities. Just as I will continue to love bagels, babka, Yiddish, and Fiddler on the Roof, I think it’s about time we do the same for Ladino.';
$meta_keywords = 'jewish, spanish, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = 'https://jewishstudies.washington.edu/wp-content/uploads/2013/10/Bunis-cropped-for-header-again.png';
$related_posts = [
    ['title' => 'Alienation', 'url' => PathHelper::getCategoryIndexPath('alienation'), 'excerpt' => 'Browse all Alienation posts'],
    ['title' => 'Climate', 'url' => PathHelper::getCategoryIndexPath('climate'), 'excerpt' => 'Browse all Climate posts'],
    ['title' => 'Humor', 'url' => PathHelper::getCategoryIndexPath('humor'), 'excerpt' => 'Browse all Humor posts'],
    ['title' => 'Inspiration', 'url' => PathHelper::getCategoryIndexPath('inspiration'), 'excerpt' => 'Browse all Inspiration posts'],
    ['title' => 'Journal', 'url' => PathHelper::getCategoryIndexPath('journal'), 'excerpt' => 'Browse all Journal posts'],
    ['title' => 'Judaism', 'url' => PathHelper::getCategoryIndexPath('judaism'), 'excerpt' => 'Browse all Judaism posts'],
    ['title' => 'Kitchen', 'url' => PathHelper::getCategoryIndexPath('kitchen'), 'excerpt' => 'Browse all Kitchen posts'],
    ['title' => 'Ptsd Myth', 'url' => PathHelper::getCategoryIndexPath('ptsd-myth'), 'excerpt' => 'Browse all Ptsd Myth posts'],
    ['title' => 'Street', 'url' => PathHelper::getCategoryIndexPath('street'), 'excerpt' => 'Browse all Street posts'],
    ['title' => 'Writings', 'url' => PathHelper::getCategoryIndexPath('writings'), 'excerpt' => 'Browse all Writings posts'],
];

$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => '14 Ladino Phrases Every Jew Should Know - HeyAlma',
  'date' => '2019-03-15',
  'excerpt' => 'On that note of diversity, as Dr. Naar puts it, “Ladino is a language that builds bridges.” It intersects with Hebrew, Spanish, Arabic, and a whole map of countries it’s passed through. We are made stronger by using language and our commonalities. Just as I will continue to love bagels, babka, Yiddish, and Fiddler on the Roof, I think it’s about time we do the same for Ladino.',
  'tags' => 
  array (
    0 => 'jewish',
    1 => 'spanish',
  ),
  'categories' => 
  array (
    0 => 'Jewish',
    1 => 'Ladino',
    2 => 'Judaism',
  ),
  'author' => 'Frances Johnson - HeyAlma',
  'thumbnail' => 'https://jewishstudies.washington.edu/wp-content/uploads/2013/10/Bunis-cropped-for-header-again.png',
  'source_file' => 'content\\judaism\\ladino-phrases.md',
);

// Raw content
$post_content = '<h2>14 Ladino Phrases -HeyAlma</h2>
<p>Original by Frances Johnson March 15, 2019</p>
<p><a href="https://www.heyalma.com/14-ladino-phrases-every-jew-should-know/" target="_blank">https://www.heyalma.com/14-ladino-phrases-every-jew-should-know/</a></p>

<img src="images/ladino.jpg" width="500">

<h3>1. “Guay de mi”</h3>

<p>The equivalent of the universally applicable “oy.” My personal favorite.</p>

<h3>2. “Haberes buenos”</h3>

<p>“Good news,” for those optimistic moments.</p>

<h3>3. “Echar lashon”</h3>

<p>The equivalent of “schmooze,” a Jewish pastime.</p>

<h3>4. “Bavajadas”</h3>

<p>“Nonsense.” A perfect exclamation.</p>

<h3>5. “Bivas, kreskas, engrandeskas, komo un peshiko en aguas freskas! Amen!”</h3>

<p>A wayyyy too long “bless you.” I dare you to say this one five times fast.</p>

<h3>6. “Aksi bashi”</h3>

<p>“A grouch.” Probably an uncle.</p>

<h3>7. “Kapara”</h3>

<p>Essentially “the mistake could be worse.” A better version of ~shrug~.</p>

<h3>8. “Me vas a tratar un ayiscrin”</h3>

<p>“You are treating me to an ice cream.” Perhaps the most important demand; this should be used 24/7.</p>

<h3>9. “Djente de piron”</h3>

<p>“The one percent” or literally “people of the fork.” Did I include this so I could be the Sephardic Bernie Sanders? Definitely. Universal health care and utensils for all!</p>

<h3>10. “Hadras i baranas”</h3>

<p>“A big fuss.” Probably an aunt.</p>

<h3>11. “Engleneate!”</h3>

<p>“Have fun!” What your mom says to you after making sure you have a coat and a fully charged cell phone.</p>

<h3>12. “Las anyadas non azen sezudos, eyas non azen ke viejos”</h3>

<p>“The years don’t make people wise, they just make them old.” If my sources are correct, this is what I believe the kids call savage.</p>

<h3>13. “Kuando se eskurese es para amaneser”</h3>

<p>“When it’s dark out, that’s because dawn is coming.” See, I can be optimistic! This one particularly rings true and provides hope for our scary, uncertain times.</p>

<h3>14. “Todos los dedos de la mano no son unos”</h3>

<p>“All the fingers of the hand are not the same,” AKA we are all different. Amen.</p>

<p>On that note of diversity, as Dr. Naar puts it, “Ladino is a language that builds bridges.” It intersects with Hebrew, Spanish, Arabic, and a whole map of countries it’s passed through. We are made stronger by using language and our commonalities. Just as I will continue to love bagels, babka, Yiddish, and Fiddler on the Roof, I think it’s about time we do the same for Ladino.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>