<?php
/**
 * Google OAuth Authentication Handler
 * For A. A. Chips' Obsidian-Quartz Comments System
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once __DIR__ . '/database.php';

class GoogleAuth {
    private $config;
    private $db;

    public function __construct() {
        $this->db = CommentDatabase::getInstance();
        $this->config = $this->db->getConfig('google');
    }

    public function getAuthUrl($state = null) {
        $params = [
            'client_id' => $this->config['client_id'],
            'redirect_uri' => $this->config['redirect_uri'],
            'scope' => implode(' ', $this->config['scopes']),
            'response_type' => 'code',
            'access_type' => 'offline',
            'prompt' => 'consent'
        ];

        if ($state) {
            $params['state'] = $state;
        }

        return 'https://accounts.google.com/o/oauth2/auth?' . http_build_query($params);
    }

    public function handleCallback() {
        if (!isset($_GET['code'])) {
            throw new Exception('Authorization code not received');
        }

        $code = $_GET['code'];
        $state = $_GET['state'] ?? null;

        // Exchange code for access token
        $tokenData = $this->exchangeCodeForToken($code);
        
        // Get user info from Google
        $userInfo = $this->getUserInfo($tokenData['access_token']);
        
        // Create or update user in database
        $user = $this->db->createOrUpdateUser(
            $userInfo['id'],
            $userInfo['email'],
            $userInfo['name'],
            $userInfo['picture'] ?? null
        );

        // Set session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_google_id'] = $user['google_id'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_picture'] = $user['picture_url'];
        $_SESSION['is_admin'] = $this->isAdmin($user['google_id']);

        // Redirect back to the page they came from
        $redirectUrl = $state ? base64_decode($state) : '/';
        header('Location: ' . $redirectUrl);
        exit;
    }

    private function exchangeCodeForToken($code) {
        $postData = [
            'client_id' => $this->config['client_id'],
            'client_secret' => $this->config['client_secret'],
            'redirect_uri' => $this->config['redirect_uri'],
            'grant_type' => 'authorization_code',
            'code' => $code
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception('Failed to exchange code for token');
        }

        $tokenData = json_decode($response, true);
        if (!$tokenData || !isset($tokenData['access_token'])) {
            throw new Exception('Invalid token response');
        }

        return $tokenData;
    }

    private function getUserInfo($accessToken) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://www.googleapis.com/oauth2/v2/userinfo');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $accessToken
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception('Failed to get user info');
        }

        $userInfo = json_decode($response, true);
        if (!$userInfo || !isset($userInfo['id'])) {
            throw new Exception('Invalid user info response');
        }

        return $userInfo;
    }

    private function isAdmin($googleId) {
        $adminIds = $this->db->getConfig('admin.admin_google_ids');
        return in_array($googleId, $adminIds);
    }

    public static function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }

    public static function getCurrentUser() {
        if (!self::isLoggedIn()) {
            return null;
        }

        return [
            'id' => $_SESSION['user_id'],
            'google_id' => $_SESSION['user_google_id'],
            'name' => $_SESSION['user_name'],
            'email' => $_SESSION['user_email'],
            'picture_url' => $_SESSION['user_picture'],
            'is_admin' => $_SESSION['is_admin'] ?? false
        ];
    }

    public static function logout() {
        session_destroy();
    }

    public static function requireLogin($redirectUrl = null) {
        if (!self::isLoggedIn()) {
            $auth = new self();
            $state = $redirectUrl ? base64_encode($redirectUrl) : null;
            header('Location: ' . $auth->getAuthUrl($state));
            exit;
        }
    }
}

// Handle the callback if we're being called directly
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['code'])) {
    try {
        $auth = new GoogleAuth();
        $auth->handleCallback();
    } catch (Exception $e) {
        error_log('Google Auth Error: ' . $e->getMessage());
        header('HTTP/1.1 400 Bad Request');
        echo 'Authentication failed: ' . htmlspecialchars($e->getMessage());
        exit;
    }
}

// Handle logout
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['logout'])) {
    GoogleAuth::logout();
    $redirectUrl = $_GET['redirect'] ?? '/';
    header('Location: ' . $redirectUrl);
    exit;
}

// If accessed directly without parameters, show login page
if ($_SERVER['REQUEST_METHOD'] === 'GET' && !isset($_GET['code']) && !isset($_GET['logout'])) {
    $auth = new GoogleAuth();
    $redirectUrl = $_GET['redirect'] ?? '/';
    $state = base64_encode($redirectUrl);
    header('Location: ' . $auth->getAuthUrl($state));
    exit;
}
?>
