Stack trace:
Frame         Function      Args
0007FFFFBDD0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFACD0) msys-2.0.dll+0x2118E
0007FFFFBDD0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFBDD0  0002100469F2 (00021028DF99, 0007FFFFBC88, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBDD0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBDD0  00021006A545 (0007FFFFBDE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFBDE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9B2340000 ntdll.dll
7FF9B0F90000 KERNEL32.DLL
7FF9AF700000 KERNELBASE.dll
7FF9B1BA0000 USER32.dll
7FF9AF620000 win32u.dll
7FF9B0520000 GDI32.dll
7FF9AFFC0000 gdi32full.dll
7FF9AF650000 msvcp_win.dll
7FF9AF4D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF9B05D0000 advapi32.dll
7FF9B0D10000 msvcrt.dll
7FF9B0EE0000 sechost.dll
7FF9B0100000 RPCRT4.dll
7FF9AEAC0000 CRYPTBASE.DLL
7FF9AFC50000 bcryptPrimitives.dll
7FF9B0300000 IMM32.DLL
